# CLAUDE.md

This file provides guidance to Claude <PERSON> (claude.ai/code) when working with code in this repository.

## Project Overview

This is a Jira Forge app that provides AI-powered requirements expansion. When users update the "Prompt" custom field in Jira issues, the app automatically expands them into detailed requirements using OpenRouter AI and populates the "AI Requirements" field. The app includes a comprehensive admin interface for configuring AI settings and customizing templates per issue type.

## Common Commands

- `npm test` / `npm run test:unit` - Run unit tests
- `npm run test:integration` - Run integration tests (requires `forge deploy` first)
- `forge lint` - Run ESLint
- `forge deploy` - Deploy to Forge platform

## Naming Conventions

- **Branch format**: `feature/PLUG-##-Title`, `task/PLUG-##-Title`, `bugfix/PLUG-##-Title`
- **Jira tickets**: `PLUG-##` format (Story, Task, Bug types)
- Always include Jira ticket number in branches, commits, and PR titles

## Architecture

### Core Flow
1. **Trigger**: Jira `avi:jira:updated:issue` event fires when issues are updated
2. **Handler**: `promptUpdateHandler` processes events and enqueues async processing
3. **Field Resolution**: Uses `JiraService` to resolve custom field IDs and extract requirements
4. **Queue Processing**: Uses `QueueService` to enqueue async requirements processing  
5. **Async Handler**: `requirementsProcessorHandler` orchestrates the AI workflow
6. **AI Processing**: Uses `AIService` to create prompts and expand requirements
7. **Content Processing**: Uses `RequirementsService` to validate, clean, and convert to ADF format
8. **Update**: Uses `JiraService` to update Jira issue with expanded requirements

### Key Components

#### Resolvers (`src/resolvers/index.js`)
- Lightweight orchestrators that delegate to focused services
- `promptUpdateHandler`: Validates events and enqueues async processing
- `requirementsProcessorHandler`: Coordinates AI workflow (AI → Requirements → Jira services)
- Each service handles a single responsibility with minimal cross-dependencies

#### Services Layer
- **`src/services/AIService.js`**: AI interactions (prompt creation, OpenRouter API calls)
- **`src/services/RequirementsService.js`**: Content processing (validation, cleaning, ADF conversion)
- **`src/services/JiraService.js`**: Jira operations (field resolution, issue updates, changelog analysis)
- **`src/services/QueueService.js`**: Event queue management for async processing

#### Utilities
- **`src/utils/jira-helpers.js`**: Low-level Jira API interactions, field resolution, change detection
- **`src/utils/openrouter-client.js`**: Low-level OpenRouter API client (HTTP requests only)
- **`src/config/index.js`**: Centralized configuration management with environment variable handling

#### Custom Fields (defined in `manifest.yml`)
- `prompt-field`: Input for basic prompts
- `ai-requirements-field`: Auto-populated with AI-expanded requirements


### Dependencies
- **`@forge/api`**: Core Forge platform integration
- **`@forge/resolver`**: Event handling framework
- **`marklassian`**: Markdown to ADF conversion for Jira rich text
- **OpenRouter**: External AI service (DeepSeek Chat v3 model)

## Environment Configuration

**Required**: `SECRET_OPENROUTER_API_KEY` (set via `forge variables set --encrypt`)
**Optional**: `OPENROUTER_MODEL`, `OPENROUTER_TEMPERATURE`, `OPENROUTER_MAX_TOKENS` (configured in `src/config/index.js`)

## Admin Configuration

The app includes a comprehensive admin interface accessible to project administrators through the Jira project settings.

### Admin Page Access
- **Location**: "AI Requirements Settings" in Jira project settings (requires project admin access)

### Features

#### Templates Management
- **Issue Type Templates**: Customize AI expansion templates for different issue types (Story, Bug, Task, etc.)
- **Per-Issue Type Configuration**: Each issue type can have its own specialized template
- **Markdown Editor**: Full-featured editor with live preview for template editing
- **Template Placeholders**: Support for `{curly bracket}` placeholders and instructions
- **Bulk Operations**: Reset individual templates or all templates to defaults
- **Unsaved Changes Detection**: Warns before losing work

#### AI Settings Configuration  
- **Model Selection**: Configure the OpenRouter AI model identifier
- **Temperature Control**: Adjust AI creativity/randomness (0-2 scale)
- **Token Limits**: Set maximum response length (1-4000 tokens)
- **Reset to Defaults**: Restore default parameters

### Technical Implementation

#### Key Components
- **Frontend**: `static/admin-page/src/index.jsx` - React app
- **Resolver**: `src/resolvers/admin-resolver.js` - Backend API
- **Settings Service**: `src/services/SettingsService.js` - Project-scoped storage
- **Storage**: Project-scoped settings with default templates for all issue types

## Development Notes

### Custom Field Resolution
The app dynamically resolves custom field IDs by name rather than hardcoding them, making it portable across different Jira instances. The `getCustomFieldId` function in `src/utils/jira-helpers.js` supports batch field resolution, accepting either a single field key or an array of field keys to optimize API calls.

### AI Integration
- Uses DeepSeek Chat v3 model via OpenRouter (free tier)
- Configured with 0.7 temperature, 1000 max tokens, 20-second timeout
- Structured prompts for consistent Markdown-formatted responses
- All AI configuration is centralized in `src/config/index.js`

