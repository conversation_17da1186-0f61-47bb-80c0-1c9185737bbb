import { jest } from '@jest/globals';

// Create mock for @forge/api
const mockFetch = jest.fn();
const mockApi = {
  fetch: mockFetch
};

jest.unstable_mockModule('@forge/api', () => ({
  default: mockApi
}));

// Import the module under test after mocking
const { callOpenRouter } = await import('../../../src/utils/openrouter-client.js');

// Test data
const mockExpandedRequirements = `# Expanded Requirements

## Overview
This is the expanded version of the requirements.

## Acceptance Criteria
- Criterion 1
- Criterion 2
- Criterion 3

## Technical Notes
Additional technical details here.`;

const createMockOpenRouterResponse = () => ({
  ok: true,
  status: 200,
  json: jest.fn().mockResolvedValue({
    choices: [
      {
        message: {
          content: mockExpandedRequirements
        }
      }
    ]
  })
});

const mockOpenRouterErrorResponse = {
  ok: false,
  status: 500,
  statusText: 'Internal Server Error',
  text: jest.fn().mockResolvedValue('API Error')
};

describe('openrouter-client', () => {
  const originalEnv = process.env;

  beforeEach(() => {
    jest.clearAllMocks();
    process.env = { ...originalEnv };
    process.env.SECRET_OPENROUTER_API_KEY = 'test-api-key';
  });

  afterEach(() => {
    process.env = originalEnv;
  });

  describe('callOpenRouter', () => {
    const fullPrompt = 'You are a specialized requirements expansion assistant for Jira user stories. Transform: Create user login functionality';

    it('should successfully call OpenRouter API', async () => {
      mockFetch.mockResolvedValue(createMockOpenRouterResponse());

      const result = await callOpenRouter(fullPrompt);

      expect(result).toBe(mockExpandedRequirements);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key',
            'HTTP-Referer': 'https://project0jiraplugin.atlassian.net',
            'X-Title': 'Jira Requirements Expander'
          }),
          body: expect.stringContaining(fullPrompt)
        })
      );
    });

    it('should return null if API key is not set', async () => {
      delete process.env.SECRET_OPENROUTER_API_KEY;

      const result = await callOpenRouter(fullPrompt);

      expect(result).toBeNull();
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should return null if prompt is empty', async () => {
      const result = await callOpenRouter('');

      expect(result).toBeNull();
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should return null if prompt is whitespace only', async () => {
      const result = await callOpenRouter('   ');

      expect(result).toBeNull();
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should return null if prompt is null', async () => {
      const result = await callOpenRouter(null);

      expect(result).toBeNull();
      expect(mockFetch).not.toHaveBeenCalled();
    });

    it('should handle API error response', async () => {
      mockFetch.mockResolvedValue(mockOpenRouterErrorResponse);

      const result = await callOpenRouter(fullPrompt);

      expect(result).toBeNull();
    });

    it('should handle network errors', async () => {
      mockFetch.mockRejectedValue(new Error('Network failed'));

      const result = await callOpenRouter(fullPrompt);

      expect(result).toBeNull();
    });

    it('should handle timeout (handled by Forge async events)', async () => {
      // Mock a response that takes a long time (simulating network delay)
      const delayedResponse = new Promise(resolve => {
        setTimeout(() => resolve(createMockOpenRouterResponse()), 50);
      });
      mockFetch.mockReturnValue(delayedResponse);

      const result = await callOpenRouter(fullPrompt);

      // With async events, the timeout is handled at the Forge level
      // This test just ensures the client can handle delayed responses
      expect(result).toBe(mockExpandedRequirements);
    });

    it('should handle unexpected response format', async () => {
      const unexpectedResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({
          error: 'Unexpected format'
        })
      };
      mockFetch.mockResolvedValue(unexpectedResponse);

      const result = await callOpenRouter(fullPrompt);

      expect(result).toBeNull();
    });

    it('should handle empty choices array', async () => {
      const emptyChoicesResponse = {
        ok: true,
        status: 200,
        json: jest.fn().mockResolvedValue({
          choices: []
        })
      };
      mockFetch.mockResolvedValue(emptyChoicesResponse);

      const result = await callOpenRouter(fullPrompt);

      expect(result).toBeNull();
    });

    it('should use custom configuration options', async () => {
      const customOptions = {
        model: 'custom-model',
        temperature: 0.5,
        maxTokens: 1000
      };
      mockFetch.mockResolvedValue(createMockOpenRouterResponse());

      const result = await callOpenRouter(fullPrompt, customOptions);

      expect(result).toBe(mockExpandedRequirements);
      expect(mockFetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          body: expect.stringContaining('custom-model')
        })
      );
    });

    it('should make correct API request with proper headers and body', async () => {
      mockFetch.mockResolvedValue(createMockOpenRouterResponse());

      await callOpenRouter(fullPrompt);

      expect(mockFetch).toHaveBeenCalledWith(
        'https://openrouter.ai/api/v1/chat/completions',
        expect.objectContaining({
          method: 'POST',
          headers: expect.objectContaining({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer test-api-key',
            'HTTP-Referer': 'https://project0jiraplugin.atlassian.net',
            'X-Title': 'Jira Requirements Expander'
          }),
          body: expect.stringContaining(fullPrompt)
        })
      );
    });

    it('should send prompt as single user message in API request', async () => {
      mockFetch.mockResolvedValue(createMockOpenRouterResponse());

      await callOpenRouter(fullPrompt);

      const callArgs = mockFetch.mock.calls[0];
      const requestBody = JSON.parse(callArgs[1].body);

      expect(requestBody.messages).toHaveLength(1);
      expect(requestBody.messages[0].role).toBe('user');
      expect(requestBody.messages[0].content).toBe(fullPrompt);
    });

  });

});