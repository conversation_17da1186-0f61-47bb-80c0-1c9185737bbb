// Import the module under test
const { createFullPrompt, SYSTEM_PROMPT_TEMPLATE, getDefaultTemplate, STORY_TEMPLATE, TASK_TEMPLATE, BUG_TEMPLATE, EPIC_TEMPLATE, SUBTASK_TEMPLATE } = await import('../../../build/system-prompt.js');

describe('system-prompt', () => {
  describe('SYSTEM_PROMPT_TEMPLATE', () => {
    it('should contain the expected template placeholders', () => {
      expect(SYSTEM_PROMPT_TEMPLATE).toContain('{{ISSUE_TITLE}}');
      expect(SYSTEM_PROMPT_TEMPLATE).toContain('{{ISSUE_REQUIREMENTS}}');
      expect(SYSTEM_PROMPT_TEMPLATE).toContain('{{RESPONSE_TEMPLATE}}');
    });
  });

  describe('getDefaultTemplate', () => {
    it('should return story template for story issue type', () => {
      const template = getDefaultTemplate('story');
      expect(template).toBe(STORY_TEMPLATE);
      expect(template).toContain('### User Story');
    });

    it('should return task template for task issue type', () => {
      const template = getDefaultTemplate('task');
      expect(template).toBe(TASK_TEMPLATE);
      expect(template).toContain('### Task Description');
    });

    it('should return bug template for bug issue type', () => {
      const template = getDefaultTemplate('bug');
      expect(template).toBe(BUG_TEMPLATE);
      expect(template).toContain('### Bug Summary');
    });

    it('should return epic template for epic issue type', () => {
      const template = getDefaultTemplate('epic');
      expect(template).toBe(EPIC_TEMPLATE);
      expect(template).toContain('### Epic Overview');
    });

    it('should return subtask template for subtask issue type', () => {
      const template = getDefaultTemplate('subtask');
      expect(template).toBe(SUBTASK_TEMPLATE);
      expect(template).toContain('### Subtask Description');
    });

    it('should return empty template for unknown issue types', () => {
      const template = getDefaultTemplate('unknown-type');
      expect(template).toBe('');
    });

    it('should handle case insensitive issue types', () => {
      expect(getDefaultTemplate('STORY')).toBe(STORY_TEMPLATE);
      expect(getDefaultTemplate('Task')).toBe(TASK_TEMPLATE);
      expect(getDefaultTemplate('BUG')).toBe(BUG_TEMPLATE);
    });

    it('should default to story template for null/undefined', () => {
      expect(getDefaultTemplate(null)).toBe(STORY_TEMPLATE);
      expect(getDefaultTemplate(undefined)).toBe(STORY_TEMPLATE);
    });
  });

  describe('createFullPrompt', () => {
    const testUserInput = 'Create a user login system';
    const testTitle = 'User Login Feature';

    it('should replace {{ISSUE_TITLE}} placeholder with actual title', () => {
      const result = createFullPrompt(testTitle, testUserInput, STORY_TEMPLATE);
      
      expect(result).toContain(testTitle);
      expect(result).not.toContain('{{ISSUE_TITLE}}');
    });

    it('should replace {{ISSUE_REQUIREMENTS}} placeholder with actual user input', () => {
      const result = createFullPrompt(testTitle, testUserInput, STORY_TEMPLATE);
      
      expect(result).toContain(testUserInput);
      expect(result).not.toContain('{{ISSUE_REQUIREMENTS}}');
    });

    it('should replace {{RESPONSE_TEMPLATE}} placeholder with the actual template', () => {
      const result = createFullPrompt(testTitle, testUserInput, STORY_TEMPLATE);
      
      expect(result).toContain('### Acceptance Criteria');
      expect(result).not.toContain('{{RESPONSE_TEMPLATE}}');
    });

    it('should preserve the core prompt structure after substitution', () => {
      const result = createFullPrompt(testTitle, testUserInput, STORY_TEMPLATE);
      
      expect(result).toContain('requirements expansion assistant');
      expect(result).toContain(testUserInput);
    });

    it('should handle empty user input gracefully', () => {
      const result = createFullPrompt(testTitle, '', STORY_TEMPLATE);
      
      expect(result).toContain('### Acceptance Criteria');
      expect(result).not.toContain('{{ISSUE_REQUIREMENTS}}');
      expect(result).not.toContain('{{RESPONSE_TEMPLATE}}');
    });

    it('should handle special characters in user input', () => {
      const specialInput = 'Create login with "quotes" & symbols';
      const result = createFullPrompt(testTitle, specialInput, TASK_TEMPLATE);
      
      expect(result).toContain(specialInput);
      expect(result).not.toContain('{{ISSUE_REQUIREMENTS}}');
    });

    it('should produce a complete system prompt with main placeholders replaced', () => {
      const result = createFullPrompt(testTitle, testUserInput, BUG_TEMPLATE);
      
      // Ensure main template placeholders are replaced
      expect(result).not.toContain('{{ISSUE_TITLE}}');
      expect(result).not.toContain('{{ISSUE_REQUIREMENTS}}');
      expect(result).not.toContain('{{RESPONSE_TEMPLATE}}');
      
      // Ensure it contains expected content
      expect(result.length).toBeGreaterThan(500); // Should be a substantial prompt
    });

    it('should use different templates for different issue types', () => {
      const storyResult = createFullPrompt(testTitle, testUserInput, STORY_TEMPLATE);
      const taskResult = createFullPrompt(testTitle, testUserInput, TASK_TEMPLATE);
      const bugResult = createFullPrompt(testTitle, testUserInput, BUG_TEMPLATE);
      
      // Story template should contain user story structure
      expect(storyResult).toContain('### User Story');
      expect(storyResult).not.toContain('### Task Description');
      
      // Task template should contain task structure
      expect(taskResult).toContain('### Task Description');
      expect(taskResult).not.toContain('### User Story');
      
      // Bug template should contain bug structure
      expect(bugResult).toContain('### Bug Summary');
      expect(bugResult).not.toContain('### User Story');
    });

    it('should handle empty template gracefully', () => {
      const result = createFullPrompt(testTitle, testUserInput, '');
      
      expect(result).toContain(testTitle);
      expect(result).toContain(testUserInput);
      expect(result).not.toContain('{{ISSUE_TITLE}}');
      expect(result).not.toContain('{{ISSUE_REQUIREMENTS}}');
      expect(result).not.toContain('{{RESPONSE_TEMPLATE}}');
    });

    it('should handle missing template parameter', () => {
      // The function signature now requires template parameter, but it handles undefined gracefully
      const result = createFullPrompt(testTitle, testUserInput, undefined);
      expect(result).toContain(testTitle);
      expect(result).toContain(testUserInput);
      expect(result).toContain('undefined'); // undefined gets inserted as string
    });
  });
});