import { jest } from '@jest/globals';

// Test data
const mockSettings = {
  aiModel: 'deepseek/deepseek-chat-v3-0324:free',
  temperature: '0.7',
  maxTokens: '1000'
};

const mockTemplates = {
  story: 'Story template content',
  task: 'Task template content',
  bug: 'Bug template content'
};

const mockIssueTypes = [
  { id: '10001', name: 'Story', description: 'User story' },
  { id: '10002', name: 'Task', description: 'Task' },
  { id: '10003', name: 'Bug', description: 'Bug' }
];

// Create mock functions for service-level mocking
const mockGetSettings = jest.fn();
const mockSaveSettings = jest.fn();
const mockResetToDefaults = jest.fn();
const mockGetTemplateForIssueType = jest.fn();
const mockSaveTemplateForIssueType = jest.fn();
const mockResetTemplatesToDefaults = jest.fn();
const mockGetTemplatesForIssueTypes = jest.fn();
const mockGetProjectIssueTypes = jest.fn();
const mockDeleteTemplateForIssueType = jest.fn();
const mockGetDefaultTemplateForIssueType = jest.fn();

// Mock services at the service level
jest.unstable_mockModule('../../../src/services/SettingsService.js', () => {
  class MockSettingsService {
    constructor() {}
    async getSettings(projectId) { return mockGetSettings(projectId); }
    async saveSettings(settings, projectId) { return mockSaveSettings(settings, projectId); }
    async resetToDefaults(projectId) { return mockResetToDefaults(projectId); }
    async getTemplateForIssueType(issueType, projectId) { return mockGetTemplateForIssueType(issueType, projectId); }
    async saveTemplateForIssueType(issueType, template, projectId) { return mockSaveTemplateForIssueType(issueType, template, projectId); }
    async resetTemplatesToDefaults(projectId) { return mockResetTemplatesToDefaults(projectId); }
    async getTemplatesForIssueTypes(issueTypes, projectId) { return mockGetTemplatesForIssueTypes(issueTypes, projectId); }
    async getAllTemplates(projectId) { return mockTemplates; }
    async deleteTemplateForIssueType(issueType, projectId) { return mockDeleteTemplateForIssueType(issueType, projectId); }
    getDefaultTemplateForIssueType(issueType) { return mockGetDefaultTemplateForIssueType(issueType); }
  }
  
  return { SettingsService: MockSettingsService };
});

jest.unstable_mockModule('../../../src/services/JiraService.js', () => {
  class MockJiraService {
    constructor() {}
    async getProjectIssueTypes(projectId) { return mockGetProjectIssueTypes(projectId); }
  }
  
  return { JiraService: MockJiraService };
});

// Import the module under test after mocking
const { adminHandler } = await import('../../../src/resolvers/admin-resolver.js');

describe('admin-resolver', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('adminHandler', () => {
    const mockRequest = (method, payload = {}) => ({
      call: {
        payload: {
          method,
          payload
        }
      },
      context: {
        extension: {
          project: {
            id: '12345'
          }
        }
      }
    });

    describe('getSettings', () => {
      it('should return settings successfully', async () => {
        mockGetSettings.mockResolvedValue(mockSettings);

        const result = await adminHandler(mockRequest('getSettings'));

        expect(mockGetSettings).toHaveBeenCalledWith('12345');
        expect(result).toEqual({
          success: true,
          settings: mockSettings
        });
      });

      it('should handle settings service error', async () => {
        mockGetSettings.mockRejectedValue(new Error('Settings load failed'));

        const result = await adminHandler(mockRequest('getSettings'));

        expect(result).toEqual({
          success: false,
          error: 'Failed to load settings'
        });
      });
    });

    describe('saveSettings', () => {
      it('should save settings successfully', async () => {
        mockSaveSettings.mockResolvedValue(true);

        const result = await adminHandler(mockRequest('saveSettings', { settings: mockSettings }));

        expect(mockSaveSettings).toHaveBeenCalledWith(mockSettings, '12345');
        expect(result).toEqual({
          success: true,
          message: 'Settings saved successfully'
        });
      });

      it('should return error when settings data is missing', async () => {
        const result = await adminHandler(mockRequest('saveSettings', {}));

        expect(result).toEqual({
          success: false,
          error: 'Settings data is required'
        });
        expect(mockSaveSettings).not.toHaveBeenCalled();
      });

      it('should validate settings and return error for invalid temperature', async () => {
        const invalidSettings = { ...mockSettings, temperature: '3.0' };

        const result = await adminHandler(mockRequest('saveSettings', { settings: invalidSettings }));

        expect(result).toEqual({
          success: false,
          error: 'Temperature must be a number between 0 and 2'
        });
        expect(mockSaveSettings).not.toHaveBeenCalled();
      });

      it('should validate settings and return error for invalid max tokens', async () => {
        const invalidSettings = { ...mockSettings, maxTokens: '5000' };

        const result = await adminHandler(mockRequest('saveSettings', { settings: invalidSettings }));

        expect(result).toEqual({
          success: false,
          error: 'Max tokens must be a number between 1 and 4000'
        });
        expect(mockSaveSettings).not.toHaveBeenCalled();
      });


      it('should handle settings service save failure', async () => {
        mockSaveSettings.mockResolvedValue(false);

        const result = await adminHandler(mockRequest('saveSettings', { settings: mockSettings }));

        expect(result).toEqual({
          success: false,
          error: 'Failed to save settings'
        });
      });

      it('should handle settings service error', async () => {
        mockSaveSettings.mockRejectedValue(new Error('Save failed'));

        const result = await adminHandler(mockRequest('saveSettings', { settings: mockSettings }));

        expect(result).toEqual({
          success: false,
          error: 'Failed to save settings'
        });
      });
    });

    describe('resetToDefaults', () => {
      it('should reset settings to defaults successfully', async () => {
        const defaultSettings = { ...mockSettings, temperature: '0.5' };
        mockResetToDefaults.mockResolvedValue(defaultSettings);

        const result = await adminHandler(mockRequest('resetToDefaults'));

        expect(mockResetToDefaults).toHaveBeenCalledWith('12345');
        expect(result).toEqual({
          success: true,
          settings: defaultSettings,
          message: 'Settings reset to defaults'
        });
      });

      it('should handle reset service error', async () => {
        mockResetToDefaults.mockRejectedValue(new Error('Reset failed'));

        const result = await adminHandler(mockRequest('resetToDefaults'));

        expect(result).toEqual({
          success: false,
          error: 'Failed to reset settings'
        });
      });
    });

    describe('getAllTemplates', () => {
      it('should return all templates successfully', async () => {
        const result = await adminHandler(mockRequest('getAllTemplates'));

        expect(result).toEqual({
          success: true,
          templates: mockTemplates
        });
      });
    });

    describe('getTemplateByType', () => {
      it('should return template for specific issue type successfully', async () => {
        const issueType = 'story';
        const template = 'Story template content';
        mockGetTemplateForIssueType.mockResolvedValue(template);

        const result = await adminHandler(mockRequest('getTemplateByType', { issueType }));

        expect(mockGetTemplateForIssueType).toHaveBeenCalledWith(issueType, '12345');
        expect(result).toEqual({
          success: true,
          template,
          issueType
        });
      });

      it('should return error when issue type is missing', async () => {
        const result = await adminHandler(mockRequest('getTemplateByType', {}));

        expect(result).toEqual({
          success: false,
          error: 'Issue type is required'
        });
        expect(mockGetTemplateForIssueType).not.toHaveBeenCalled();
      });

      it('should handle template service error', async () => {
        mockGetTemplateForIssueType.mockRejectedValue(new Error('Template load failed'));

        const result = await adminHandler(mockRequest('getTemplateByType', { issueType: 'story' }));

        expect(result).toEqual({
          success: false,
          error: 'Failed to load template'
        });
      });
    });

    describe('saveTemplateByType', () => {
      it('should save template for specific issue type successfully', async () => {
        const issueType = 'story';
        const template = 'Updated story template';
        mockSaveTemplateForIssueType.mockResolvedValue(true);

        const result = await adminHandler(mockRequest('saveTemplateByType', { issueType, template }));

        expect(mockSaveTemplateForIssueType).toHaveBeenCalledWith(issueType, template, '12345');
        expect(result).toEqual({
          success: true,
          message: `Template saved successfully for ${issueType}`
        });
      });

      it('should return error when issue type is missing', async () => {
        const result = await adminHandler(mockRequest('saveTemplateByType', { template: 'Template content' }));

        expect(result).toEqual({
          success: false,
          error: 'Issue type and template are required'
        });
        expect(mockSaveTemplateForIssueType).not.toHaveBeenCalled();
      });

      it('should return error when template is missing', async () => {
        const result = await adminHandler(mockRequest('saveTemplateByType', { issueType: 'story' }));

        expect(result).toEqual({
          success: false,
          error: 'Issue type and template are required'
        });
        expect(mockSaveTemplateForIssueType).not.toHaveBeenCalled();
      });

      it('should validate template and return error for empty template', async () => {
        const result = await adminHandler(mockRequest('saveTemplateByType', { issueType: 'story', template: '   ' }));

        expect(result).toEqual({
          success: false,
          error: 'Template cannot be empty'
        });
        expect(mockSaveTemplateForIssueType).not.toHaveBeenCalled();
      });

      it('should validate template and return error for too long template', async () => {
        const longTemplate = 'a'.repeat(10001);
        const result = await adminHandler(mockRequest('saveTemplateByType', { issueType: 'story', template: longTemplate }));

        expect(result).toEqual({
          success: false,
          error: 'Template is too long (maximum 10,000 characters)'
        });
        expect(mockSaveTemplateForIssueType).not.toHaveBeenCalled();
      });

      it('should handle template service save failure', async () => {
        mockSaveTemplateForIssueType.mockResolvedValue(false);

        const result = await adminHandler(mockRequest('saveTemplateByType', { issueType: 'story', template: 'Template content' }));

        expect(result).toEqual({
          success: false,
          error: 'Failed to save template'
        });
      });

      it('should handle template service error', async () => {
        mockSaveTemplateForIssueType.mockRejectedValue(new Error('Save failed'));

        const result = await adminHandler(mockRequest('saveTemplateByType', { issueType: 'story', template: 'Template content' }));

        expect(result).toEqual({
          success: false,
          error: 'Failed to save template'
        });
      });
    });

    describe('resetTemplatesToDefaults', () => {
      it('should reset all templates to defaults successfully', async () => {
        const defaultTemplates = { ...mockTemplates };
        mockResetTemplatesToDefaults.mockResolvedValue(defaultTemplates);

        const result = await adminHandler(mockRequest('resetTemplatesToDefaults'));

        expect(mockResetTemplatesToDefaults).toHaveBeenCalledWith('12345');
        expect(result).toEqual({
          success: true,
          templates: defaultTemplates,
          message: 'All templates reset to defaults'
        });
      });

      it('should handle reset templates service error', async () => {
        mockResetTemplatesToDefaults.mockRejectedValue(new Error('Reset failed'));

        const result = await adminHandler(mockRequest('resetTemplatesToDefaults'));

        expect(result).toEqual({
          success: false,
          error: 'Failed to reset templates: Reset failed'
        });
      });
    });

    describe('getProjectIssueTypes', () => {
      it('should return project issue types successfully', async () => {
        const projectId = '10000';
        mockGetProjectIssueTypes.mockResolvedValue(mockIssueTypes);

        const result = await adminHandler(mockRequest('getProjectIssueTypes', { projectId }));

        expect(mockGetProjectIssueTypes).toHaveBeenCalledWith(projectId);
        expect(result).toEqual({
          success: true,
          issueTypes: mockIssueTypes
        });
      });

      it('should return error when project ID is missing', async () => {
        const result = await adminHandler(mockRequest('getProjectIssueTypes', {}));

        expect(result).toEqual({
          success: false,
          error: 'Project ID is required'
        });
        expect(mockGetProjectIssueTypes).not.toHaveBeenCalled();
      });

      it('should handle Jira service error', async () => {
        mockGetProjectIssueTypes.mockRejectedValue(new Error('Jira API failed'));

        const result = await adminHandler(mockRequest('getProjectIssueTypes', { projectId: '10000' }));

        expect(result).toEqual({
          success: false,
          error: 'Failed to load project issue types'
        });
      });
    });

    describe('getTemplatesForIssueTypes', () => {
      it('should return templates for multiple issue types successfully', async () => {
        const issueTypes = ['story', 'task', 'bug'];
        mockGetTemplatesForIssueTypes.mockResolvedValue(mockTemplates);

        const result = await adminHandler(mockRequest('getTemplatesForIssueTypes', { issueTypes }));

        expect(mockGetTemplatesForIssueTypes).toHaveBeenCalledWith(issueTypes, '12345');
        expect(result).toEqual({
          success: true,
          templates: mockTemplates
        });
      });

      it('should return error when issue types array is missing', async () => {
        const result = await adminHandler(mockRequest('getTemplatesForIssueTypes', {}));

        expect(result).toEqual({
          success: false,
          error: 'Issue types array is required'
        });
        expect(mockGetTemplatesForIssueTypes).not.toHaveBeenCalled();
      });

      it('should return error when issue types is not an array', async () => {
        const result = await adminHandler(mockRequest('getTemplatesForIssueTypes', { issueTypes: 'story' }));

        expect(result).toEqual({
          success: false,
          error: 'Issue types array is required'
        });
        expect(mockGetTemplatesForIssueTypes).not.toHaveBeenCalled();
      });

      it('should handle templates service error', async () => {
        mockGetTemplatesForIssueTypes.mockRejectedValue(new Error('Templates load failed'));

        const result = await adminHandler(mockRequest('getTemplatesForIssueTypes', { issueTypes: ['story', 'task'] }));

        expect(result).toEqual({
          success: false,
          error: 'Failed to load templates for issue types'
        });
      });
    });

    describe('resetTemplateToDefault', () => {
      it('should reset single template to default successfully', async () => {
        const issueType = 'story';
        const defaultTemplate = 'Default story template';
        mockDeleteTemplateForIssueType.mockResolvedValue(true);
        mockGetDefaultTemplateForIssueType.mockReturnValue(defaultTemplate);

        const result = await adminHandler(mockRequest('resetTemplateToDefault', { issueType }));

        expect(mockDeleteTemplateForIssueType).toHaveBeenCalledWith(issueType, '12345');
        expect(mockGetDefaultTemplateForIssueType).toHaveBeenCalledWith(issueType);
        expect(result).toEqual({
          success: true,
          template: defaultTemplate,
          issueType,
          message: `Template reset to default for ${issueType}`
        });
      });

      it('should return error when issue type is missing', async () => {
        const result = await adminHandler(mockRequest('resetTemplateToDefault', {}));

        expect(result).toEqual({
          success: false,
          error: 'Issue type is required'
        });
        expect(mockDeleteTemplateForIssueType).not.toHaveBeenCalled();
        expect(mockGetDefaultTemplateForIssueType).not.toHaveBeenCalled();
      });

      it('should handle delete service error', async () => {
        mockDeleteTemplateForIssueType.mockRejectedValue(new Error('Delete failed'));

        const result = await adminHandler(mockRequest('resetTemplateToDefault', { issueType: 'story' }));

        expect(result).toEqual({
          success: false,
          error: 'Failed to reset template to default'
        });
      });
    });

    describe('unknown method', () => {
      it('should return error for unknown method', async () => {
        const result = await adminHandler(mockRequest('unknownMethod'));

        expect(result).toEqual({
          success: false,
          error: 'Unknown method: unknownMethod'
        });
      });
    });

    describe('error handling', () => {
      it('should handle malformed request structure', async () => {
        const malformedRequest = { call: {} };

        const result = await adminHandler(malformedRequest);

        expect(result).toEqual({
          success: false,
          error: "Cannot read properties of undefined (reading 'method')"
        });
      });

      it('should handle request with missing call property', async () => {
        const malformedRequest = {};

        const result = await adminHandler(malformedRequest);

        expect(result).toEqual({
          success: false,
          error: "Cannot read properties of undefined (reading 'payload')"
        });
      });
    });
  });
});