import { jest } from '@jest/globals';

// Mock jira-helpers
jest.unstable_mockModule('../../../src/utils/jira-helpers.js', () => ({
  getAllFieldsForProjectAndIssueType: jest.fn(),
  updateIssueFields: jest.fn(),
  isFieldUpdated: jest.fn(),
  getIssue: jest.fn(),
  getProjectIssueTypes: jest.fn()
}));

// Import after mocking
const { JiraService } = await import('../../../src/services/JiraService.js');
const { getAllFieldsForProjectAndIssueType, updateIssueFields, isFieldUpdated, getIssue } = await import('../../../src/utils/jira-helpers.js');

describe('JiraService', () => {
  let service;

  beforeEach(() => {
    service = new JiraService();
    jest.clearAllMocks();
  });

  describe('getFieldsByName', () => {
    it('should call getAllFieldsForProjectAndIssueType and return resolved field', async () => {
      const mockFields = [
        { id: 'customfield_10001', name: 'Test Field', key: 'test-field' },
        { id: 'customfield_10002', name: 'Another Field', key: 'another-field' }
      ];
      getAllFieldsForProjectAndIssueType.mockResolvedValue(mockFields);

      const result = await service.getFieldsByName('Test Field', '10000', '10001');

      expect(getAllFieldsForProjectAndIssueType).toHaveBeenCalledWith('10000', '10001');
      expect(result).toEqual(mockFields[0]);
    });

    it('should handle array of field names', async () => {
      const mockFields = [
        { id: 'customfield_10001', name: 'Test Field', key: 'test-field' },
        { id: 'customfield_10002', name: 'Another Field', key: 'another-field' }
      ];
      getAllFieldsForProjectAndIssueType.mockResolvedValue(mockFields);

      const result = await service.getFieldsByName(['Test Field', 'Another Field'], '10000', '10001');

      expect(getAllFieldsForProjectAndIssueType).toHaveBeenCalledWith('10000', '10001');
      expect(result).toEqual(mockFields);
    });

    it('should return null when no fields are found', async () => {
      getAllFieldsForProjectAndIssueType.mockResolvedValue([]);

      const result = await service.getFieldsByName('Test Field', '10000', '10001');

      expect(result).toBeNull();
    });
  });

  describe('updateIssueFields', () => {
    it('should call updateIssueFields utility with issue ID and fields', async () => {
      updateIssueFields.mockResolvedValue(true);

      const issueId = '10001';
      const fields = { 'customfield_10001': 'test value' };
      const result = await service.updateIssueFields(issueId, fields);

      expect(updateIssueFields).toHaveBeenCalledWith(issueId, fields);
      expect(result).toBe(true);
    });
  });

  describe('isFieldUpdated', () => {
    const mockField = { id: 'customfield_10001', name: 'Prompt' };

    it('should call isFieldUpdated utility and return result', () => {
      isFieldUpdated.mockReturnValue(true);

      const changelog = {
        items: [
          { fieldId: 'customfield_10001', field: 'Prompt', from: 'old', to: 'new' }
        ]
      };
      const result = service.isFieldUpdated(changelog, mockField);

      expect(isFieldUpdated).toHaveBeenCalledWith(changelog, mockField);
      expect(result).toBe(true);
    });

    it('should return false when field was not updated', () => {
      isFieldUpdated.mockReturnValue(false);

      const changelog = {
        items: [
          { fieldId: 'customfield_99999', field: 'Other Field', from: 'old', to: 'new' }
        ]
      };
      const result = service.isFieldUpdated(changelog, mockField);

      expect(isFieldUpdated).toHaveBeenCalledWith(changelog, mockField);
      expect(result).toBe(false);
    });
  });





  describe('extractFieldValueFromIssue', () => {
    const mockField = { id: 'customfield_10001', name: 'Prompt' };

    it('should return null when issue is null', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const result = service.extractFieldValueFromIssue(null, mockField);

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Could not fetch issue details');
      consoleSpy.mockRestore();
    });

    it('should return null when issue has no fields', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const mockIssue = { id: '10001' };

      const result = service.extractFieldValueFromIssue(mockIssue, mockField);

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Could not fetch issue details');
      consoleSpy.mockRestore();
    });

    it('should return null when field is empty', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const mockIssue = {
        id: '10001',
        fields: {
          'customfield_10001': ''
        }
      };

      const result = service.extractFieldValueFromIssue(mockIssue, mockField);

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Prompt field is empty');
      consoleSpy.mockRestore();
    });

    it('should return null when field is whitespace only', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const mockIssue = {
        id: '10001',
        fields: {
          'customfield_10001': '   '
        }
      };

      const result = service.extractFieldValueFromIssue(mockIssue, mockField);

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Prompt field is empty');
      consoleSpy.mockRestore();
    });

    it('should return null when field is missing from issue', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      const mockIssue = {
        id: '10001',
        fields: {
          // customfield_10001 is missing
        }
      };

      const result = service.extractFieldValueFromIssue(mockIssue, mockField);

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Prompt field is empty');
      consoleSpy.mockRestore();
    });

    it('should return field value when field is present and valid', () => {
      const mockIssue = {
        id: '10001',
        fields: {
          'customfield_10001': 'Create user login functionality'
        }
      };

      const result = service.extractFieldValueFromIssue(mockIssue, mockField);

      expect(result).toBe('Create user login functionality');
    });

    it('should handle undefined issue gracefully', () => {
      const consoleSpy = jest.spyOn(console, 'error').mockImplementation();
      
      const result = service.extractFieldValueFromIssue(undefined, mockField);

      expect(result).toBeNull();
      expect(consoleSpy).toHaveBeenCalledWith('Could not fetch issue details');
      consoleSpy.mockRestore();
    });
  });
});