import { jest } from '@jest/globals';

// Mock dependencies
jest.unstable_mockModule('@forge/events', () => ({
  Queue: jest.fn()
}));

jest.unstable_mockModule('../../../src/config/index.js', () => ({
  config: {
    events: {
      queues: {
        requirements: 'requirements-queue'
      }
    }
  }
}));

// Import after mocking
const { QueueService } = await import('../../../src/services/QueueService.js');
const { Queue } = await import('@forge/events');
const { config } = await import('../../../src/config/index.js');

describe('QueueService', () => {
  let service;
  let mockQueue;

  beforeEach(() => {
    service = new QueueService();
    mockQueue = {
      push: jest.fn()
    };
    Queue.mockImplementation(() => mockQueue);
    jest.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with empty queues map', () => {
      const newService = new QueueService();
      expect(newService.queues).toBeInstanceOf(Map);
      expect(newService.queues.size).toBe(0);
    });
  });

  describe('getQueue', () => {
    it('should create new queue if it does not exist', () => {
      const queueKey = 'test-queue';
      
      const result = service.getQueue(queueKey);
      
      expect(Queue).toHaveBeenCalledWith({ key: queueKey });
      expect(result).toBe(mockQueue);
      expect(service.queues.has(queueKey)).toBe(true);
    });

    it('should return existing queue if it already exists', () => {
      const queueKey = 'test-queue';
      
      // First call creates the queue
      const firstResult = service.getQueue(queueKey);
      // Second call should return the same instance
      const secondResult = service.getQueue(queueKey);
      
      expect(Queue).toHaveBeenCalledTimes(1);
      expect(firstResult).toBe(secondResult);
      expect(firstResult).toBe(mockQueue);
    });

    it('should handle multiple different queues', () => {
      const queueKey1 = 'queue-1';
      const queueKey2 = 'queue-2';
      const mockQueue2 = { push: jest.fn() };
      
      Queue.mockImplementationOnce(() => mockQueue)
           .mockImplementationOnce(() => mockQueue2);
      
      const result1 = service.getQueue(queueKey1);
      const result2 = service.getQueue(queueKey2);
      
      expect(Queue).toHaveBeenCalledTimes(2);
      expect(Queue).toHaveBeenNthCalledWith(1, { key: queueKey1 });
      expect(Queue).toHaveBeenNthCalledWith(2, { key: queueKey2 });
      expect(result1).toBe(mockQueue);
      expect(result2).toBe(mockQueue2);
      expect(service.queues.size).toBe(2);
    });
  });

  describe('enqueueRequirementsProcessing', () => {
    const mockJobData = {
      issue: {
        id: '10001',
        key: 'TEST-1',
        fields: {
          summary: 'User Login Feature',
          project: { id: '10000' },
          issuetype: { id: '10001' }
        }
      },
      simpleRequirements: 'Create user login functionality',
      fullReqFieldId: 'customfield_10002'
    };

    beforeEach(() => {
      mockQueue.push.mockResolvedValue({ jobId: 'test-job-id' });
    });

    it('should successfully enqueue requirements processing job', async () => {
      const result = await service.enqueueRequirementsProcessing(mockJobData);

      expect(service.getQueue).toBeDefined();
      expect(mockQueue.push).toHaveBeenCalledWith([{
        body: {
          issue: mockJobData.issue,
          simpleRequirements: 'Create user login functionality',
          fullReqFieldId: 'customfield_10002'
        }
      }]);
      expect(result).toBe('test-job-id');
    });

    it('should use the correct queue from config', async () => {
      await service.enqueueRequirementsProcessing(mockJobData);

      expect(Queue).toHaveBeenCalledWith({ key: config.events.queues.requirements });
    });

    it('should handle all required job data fields', async () => {
      await service.enqueueRequirementsProcessing(mockJobData);

      const expectedJobData = {
        body: {
          issue: mockJobData.issue,
          simpleRequirements: mockJobData.simpleRequirements,
          fullReqFieldId: mockJobData.fullReqFieldId
        }
      };

      expect(mockQueue.push).toHaveBeenCalledWith([expectedJobData]);
    });

    it('should throw error when queue push fails', async () => {
      const queueError = new Error('Queue push failed');
      mockQueue.push.mockRejectedValue(queueError);

      await expect(service.enqueueRequirementsProcessing(mockJobData))
        .rejects.toThrow('Queue push failed');

      expect(mockQueue.push).toHaveBeenCalledWith([{
        body: {
          issue: mockJobData.issue,
          simpleRequirements: 'Create user login functionality',
          fullReqFieldId: 'customfield_10002'
        }
      }]);
    });

    it('should handle destructured job data correctly', async () => {
      const partialJobData = {
        issue: {
          id: '10002',
          key: 'TEST-2',
          fields: {
            summary: 'Password Reset',
            project: { id: '10000' },
            issuetype: { id: '10001' }
          }
        },
        simpleRequirements: 'Users should be able to reset passwords',
        fullReqFieldId: 'customfield_10003'
      };

      await service.enqueueRequirementsProcessing(partialJobData);

      expect(mockQueue.push).toHaveBeenCalledWith([{
        body: partialJobData
      }]);
    });

    it('should handle missing optional fields gracefully', async () => {
      const minimalJobData = {
        issue: {
          id: '10003',
          key: 'TEST-3',
          fields: {
            summary: '',
            project: { id: '10000' },
            issuetype: { id: '10001' }
          }
        },
        simpleRequirements: 'Basic requirement',
        fullReqFieldId: 'customfield_10004'
      };

      const result = await service.enqueueRequirementsProcessing(minimalJobData);

      expect(result).toBe('test-job-id');
      expect(mockQueue.push).toHaveBeenCalledWith([{
        body: minimalJobData
      }]);
    });
  });

});