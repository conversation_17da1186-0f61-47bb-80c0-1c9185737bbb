import { jest } from '@jest/globals';

// Mock dependencies
jest.unstable_mockModule('../../../src/utils/openrouter-client.js', () => ({
  callOpenRouter: jest.fn()
}));

jest.unstable_mockModule('../../../build/system-prompt.js', () => ({
  createFullPrompt: jest.fn()
}));

jest.unstable_mockModule('../../../src/services/SettingsService.js', () => ({
  SettingsService: jest.fn().mockImplementation(() => ({
    getTemplateForIssueType: jest.fn().mockResolvedValue('mocked template'),
    getStoryTemplate: jest.fn().mockResolvedValue('mocked story template'),
    getAIConfig: jest.fn().mockResolvedValue({
      model: 'test-model',
      temperature: 0.7,
      maxTokens: 1000,
      apiEndpoint: 'test-endpoint'
    })
  }))
}));

// Import after mocking
const { AIService } = await import('../../../src/services/AIService.js');
const { callOpenRouter } = await import('../../../src/utils/openrouter-client.js');
const { createFullPrompt } = await import('../../../build/system-prompt.js');

describe('AIService', () => {
  let service;
  let mockSettingsService;

  beforeEach(() => {
    // Create a mock SettingsService instance
    mockSettingsService = {
      getTemplateForIssueType: jest.fn().mockResolvedValue('mocked template'),
      getStoryTemplate: jest.fn().mockResolvedValue('mocked story template'),
      getAIConfig: jest.fn().mockResolvedValue({
        model: 'test-model',
        temperature: 0.7,
        maxTokens: 1000,
        apiEndpoint: 'test-endpoint'
      })
    };
    
    service = new AIService();
    // Replace the settingsService instance with our mock
    service.settingsService = mockSettingsService;
    
    jest.clearAllMocks();
  });

  describe('expandRequirements', () => {
    const testSimpleRequirements = 'Create user login functionality';
    const testTitle = 'User Login Feature';
    const testIssueType = 'story';
    const testProjectId = '12345';
    const mockPrompt = 'Generated prompt with user requirements';
    const mockExpandedRequirements = 'Expanded requirements with details';

    beforeEach(() => {
      createFullPrompt.mockReturnValue(mockPrompt);
    });

    it('should successfully expand requirements with issue type', async () => {
      callOpenRouter.mockResolvedValue(mockExpandedRequirements);

      const result = await service.expandRequirements(testTitle, testSimpleRequirements, testIssueType, testProjectId);

      expect(mockSettingsService.getTemplateForIssueType).toHaveBeenCalledWith(testIssueType, testProjectId);
      expect(mockSettingsService.getAIConfig).toHaveBeenCalledWith(testProjectId);
      expect(createFullPrompt).toHaveBeenCalledWith(testTitle, testSimpleRequirements, 'mocked template', '');
      expect(callOpenRouter).toHaveBeenCalledWith(mockPrompt, {
        model: 'test-model',
        temperature: 0.7,
        maxTokens: 1000,
        apiEndpoint: 'test-endpoint'
      });
      expect(result).toBe(mockExpandedRequirements);
    });

    it('should pass custom options to callOpenRouter', async () => {
      const customOptions = {
        temperature: 0.5,
        maxTokens: 1000
      };
      callOpenRouter.mockResolvedValue(mockExpandedRequirements);

      const result = await service.expandRequirements(testTitle, testSimpleRequirements, testIssueType, testProjectId, customOptions);

      expect(callOpenRouter).toHaveBeenCalledWith(mockPrompt, {
        model: 'test-model',
        temperature: 0.5,  // Custom option should override
        maxTokens: 1000,   // Custom option should override
        apiEndpoint: 'test-endpoint'
      });
      expect(result).toBe(mockExpandedRequirements);
    });

    it('should return null when callOpenRouter returns null', async () => {
      callOpenRouter.mockResolvedValue(null);

      const result = await service.expandRequirements(testTitle, testSimpleRequirements, testIssueType, testProjectId);

      expect(result).toBeNull();
    });

    it('should return null when callOpenRouter returns empty string', async () => {
      callOpenRouter.mockResolvedValue('');

      const result = await service.expandRequirements(testTitle, testSimpleRequirements, testIssueType, testProjectId);

      expect(result).toBeNull();
    });

    it('should throw error when callOpenRouter throws', async () => {
      const mockError = new Error('OpenRouter API failed');
      callOpenRouter.mockRejectedValue(mockError);

      await expect(service.expandRequirements(testTitle, testSimpleRequirements, testIssueType, testProjectId))
        .rejects.toThrow('OpenRouter API failed');

      expect(createFullPrompt).toHaveBeenCalledWith(testTitle, testSimpleRequirements, 'mocked template', '');
      expect(callOpenRouter).toHaveBeenCalledWith(mockPrompt, {
        model: 'test-model',
        temperature: 0.7,
        maxTokens: 1000,
        apiEndpoint: 'test-endpoint'
      });
    });

    it('should throw error when createFullPrompt throws', async () => {
      const mockError = new Error('Prompt generation failed');
      createFullPrompt.mockImplementation(() => {
        throw mockError;
      });

      await expect(service.expandRequirements(testTitle, testSimpleRequirements, testIssueType, testProjectId))
        .rejects.toThrow('Prompt generation failed');

      expect(createFullPrompt).toHaveBeenCalledWith(testTitle, testSimpleRequirements, 'mocked template', '');
      expect(callOpenRouter).not.toHaveBeenCalled();
    });

    it('should handle undefined result from callOpenRouter', async () => {
      callOpenRouter.mockResolvedValue(undefined);

      const result = await service.expandRequirements(testTitle, testSimpleRequirements, testIssueType, testProjectId);

      expect(result).toBeNull();
    });

    it('should use default empty options when none provided', async () => {
      callOpenRouter.mockResolvedValue(mockExpandedRequirements);

      await service.expandRequirements(testTitle, testSimpleRequirements, testIssueType, testProjectId);

      expect(callOpenRouter).toHaveBeenCalledWith(mockPrompt, {
        model: 'test-model',
        temperature: 0.7,
        maxTokens: 1000,
        apiEndpoint: 'test-endpoint'
      });
    });

    it('should handle getTemplateForIssueType errors gracefully', async () => {
      const mockError = new Error('Template retrieval failed');
      mockSettingsService.getTemplateForIssueType.mockRejectedValue(mockError);

      await expect(service.expandRequirements(testTitle, testSimpleRequirements, testIssueType, testProjectId))
        .rejects.toThrow('Template retrieval failed');

      expect(mockSettingsService.getTemplateForIssueType).toHaveBeenCalledWith(testIssueType, testProjectId);
      expect(createFullPrompt).not.toHaveBeenCalled();
      expect(callOpenRouter).not.toHaveBeenCalled();
    });

    it('should use legacy story template when no issue type provided', async () => {
      callOpenRouter.mockResolvedValue(mockExpandedRequirements);
      // Mock getTemplateForIssueType to return 'mocked story template' when called with 'story'
      mockSettingsService.getTemplateForIssueType.mockResolvedValue('mocked story template');

      const result = await service.expandRequirements(testTitle, testSimpleRequirements, null, testProjectId);

      expect(mockSettingsService.getTemplateForIssueType).toHaveBeenCalledWith('story', testProjectId);
      expect(createFullPrompt).toHaveBeenCalledWith(testTitle, testSimpleRequirements, 'mocked story template', '');
      expect(result).toBe(mockExpandedRequirements);
    });

    it('should handle different issue types correctly', async () => {
      callOpenRouter.mockResolvedValue(mockExpandedRequirements);

      await service.expandRequirements(testTitle, testSimpleRequirements, 'task', testProjectId);
      expect(mockSettingsService.getTemplateForIssueType).toHaveBeenCalledWith('task', testProjectId);

      await service.expandRequirements(testTitle, testSimpleRequirements, 'bug', testProjectId);
      expect(mockSettingsService.getTemplateForIssueType).toHaveBeenCalledWith('bug', testProjectId);
    });
  });
});