/**
 * Unit tests for ConfluenceService
 */

import { jest } from '@jest/globals';

// Mock Forge API
jest.unstable_mockModule('@forge/api', () => ({
  __esModule: true,
  default: {
    asApp: jest.fn()
  },
  route: jest.fn((strings, ...values) => {
    let result = strings[0];
    for (let i = 0; i < values.length; i++) {
      result += values[i] + strings[i + 1];
    }
    return result;
  })
}));

// Mock KVS
jest.unstable_mockModule('@forge/kvs', () => ({
  kvs: {
    get: jest.fn(),
    set: jest.fn(),
    delete: jest.fn()
  }
}));

// Import after mocking
const { ConfluenceService } = await import('../../../src/services/ConfluenceService.js');
const api = (await import('@forge/api')).default;
const { route } = await import('@forge/api');
const { kvs } = await import('@forge/kvs');

describe('ConfluenceService', () => {
  let confluenceService;
  let mockRequestConfluence;

  beforeEach(() => {
    confluenceService = new ConfluenceService();
    
    // Reset all mocks
    jest.clearAllMocks();
    
    // Set up the mock after clearing
    mockRequestConfluence = jest.fn().mockResolvedValue({
      ok: true,
      json: jest.fn().mockResolvedValue({ results: [] })
    });
    
    // Mock the api.asApp().requestConfluence
    api.asApp = jest.fn().mockReturnValue({
      requestConfluence: mockRequestConfluence
    });
    
    kvs.get.mockResolvedValue(null);
    kvs.set.mockResolvedValue(undefined);
    kvs.delete.mockResolvedValue(undefined);
  });

  describe('getCacheKey', () => {
    it('should generate correct cache key', () => {
      const key = confluenceService.getCacheKey('test-key', 'project-123');
      expect(key).toBe('confluence-cache-project-project-123-test-key');
    });
  });

  describe('getSpaces', () => {
    it('should fetch spaces successfully', async () => {
      const mockSpaces = [
        { key: 'SPACE1', name: 'Space 1' },
        { key: 'SPACE2', name: 'Space 2' }
      ];
      
      mockRequestConfluence.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({ results: mockSpaces })
      });

      const result = await confluenceService.getSpaces();
      
      expect(mockRequestConfluence).toHaveBeenCalledWith('/wiki/api/v2/spaces');
      expect(result).toEqual(mockSpaces);
    });

    it('should return empty array on API error', async () => {
      mockRequestConfluence.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      const result = await confluenceService.getSpaces();
      
      expect(result).toEqual([]);
    });

    it('should handle network errors', async () => {
      mockRequestConfluence.mockRejectedValue(new Error('Network error'));

      const result = await confluenceService.getSpaces();
      
      expect(result).toEqual([]);
    });
  });

  describe('getPagesInSpace', () => {
    it('should fetch pages in space successfully', async () => {
      const mockPages = [
        { id: '123', title: 'Page 1' },
        { id: '456', title: 'Page 2' }
      ];
      
      mockRequestConfluence.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({ results: mockPages })
      });

      const result = await confluenceService.getPagesInSpace('TEST', 10);
      
      expect(mockRequestConfluence).toHaveBeenCalledWith('/wiki/api/v2/spaces/TEST/pages?limit=10&expand=version,body.view');
      expect(result).toEqual(mockPages);
    });

    it('should use default limit when not specified', async () => {
      await confluenceService.getPagesInSpace('TEST');
      
      expect(mockRequestConfluence).toHaveBeenCalledWith('/wiki/api/v2/spaces/TEST/pages?limit=25&expand=version,body.view');
    });

    it('should return empty array on API error', async () => {
      mockRequestConfluence.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      const result = await confluenceService.getPagesInSpace('TEST');
      
      expect(result).toEqual([]);
    });
  });

  describe('getPageContent', () => {
    const mockPageData = {
      id: '123',
      title: 'Test Page',
      body: {
        view: {
          value: '<p>Test content</p>'
        }
      },
      version: { number: 1 }
    };

    it('should fetch page content successfully without cache', async () => {
      mockRequestConfluence.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue(mockPageData)
      });

      const result = await confluenceService.getPageContent('123', 'project-1', false);
      
      expect(mockRequestConfluence).toHaveBeenCalledWith('/wiki/api/v2/pages/123?expand=version,body.view,space');
      expect(result).toEqual(mockPageData);
      expect(kvs.get).not.toHaveBeenCalled();
    });

    it('should check cache first when useCache is true', async () => {
      const cachedData = {
        data: mockPageData,
        timestamp: Date.now() - 1000, // 1 second ago
        version: 1
      };
      
      kvs.get.mockResolvedValue(cachedData);
      
      // Mock isCacheValid to return true
      confluenceService.isCacheValid = jest.fn().mockReturnValue(true);

      const result = await confluenceService.getPageContent('123', 'project-1', true);
      
      expect(kvs.get).toHaveBeenCalledWith('confluence-cache-project-project-1-page-123');
      expect(result).toEqual(mockPageData);
      expect(mockRequestConfluence).not.toHaveBeenCalled();
    });

    it('should fetch from API when cache is invalid', async () => {
      const staleData = {
        data: mockPageData,
        timestamp: Date.now() - 100000000, // very old
        version: 1
      };
      
      kvs.get.mockResolvedValue(staleData);
      mockRequestConfluence.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue(mockPageData)
      });

      const result = await confluenceService.getPageContent('123', 'project-1', true);
      
      expect(mockRequestConfluence).toHaveBeenCalled();
      expect(result).toEqual(mockPageData);
    });

    it('should return null on API error', async () => {
      mockRequestConfluence.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      const result = await confluenceService.getPageContent('123', 'project-1', false);
      
      expect(result).toBeNull();
    });
  });

  describe('extractPlainTextContent', () => {
    it('should extract plain text from HTML content', () => {
      const pageData = {
        body: {
          view: {
            value: '<h1>Title</h1><p>Some content</p><br><div>More content</div>'
          }
        }
      };

      const result = confluenceService.extractPlainTextContent(pageData);
      
      expect(result).toBe('Title\n\nSome content\n\n\nMore content\n');
    });

    it('should handle missing body data', () => {
      const result = confluenceService.extractPlainTextContent({});
      expect(result).toBe('');
    });

    it('should handle null page data', () => {
      const result = confluenceService.extractPlainTextContent(null);
      expect(result).toBe('');
    });

    it('should decode HTML entities', () => {
      const pageData = {
        body: {
          view: {
            value: '<p>&amp; &lt; &gt; &quot; &nbsp;</p>'
          }
        }
      };

      const result = confluenceService.extractPlainTextContent(pageData);
      expect(result).toBe('& < > "  ');
    });
  });

  describe('getProjectContext', () => {
    it('should return empty string when no page is configured', async () => {
      kvs.get.mockResolvedValue(null);

      const result = await confluenceService.getProjectContext('project-1');
      
      expect(result).toBe('');
    });

    it('should fetch and extract content from configured page', async () => {
      const mockPageData = {
        body: {
          view: {
            value: '<h1>Project Context</h1><p>Important context</p>'
          }
        },
        version: { number: 1 }
      };

      kvs.get.mockResolvedValue('page-123'); // configured page ID
      mockRequestConfluence.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue(mockPageData)
      });

      const result = await confluenceService.getProjectContext('project-1');
      
      expect(result).toBe('Project Context\n\nImportant context\n\n');
    });

    it('should return empty string when page fetch fails', async () => {
      kvs.get.mockResolvedValue('page-123');
      mockRequestConfluence.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      const result = await confluenceService.getProjectContext('project-1');
      
      expect(result).toBe('');
    });
  });

  describe('setConfiguredPageId', () => {
    it('should set configured page ID successfully', async () => {
      kvs.set.mockResolvedValue(undefined);

      const result = await confluenceService.setConfiguredPageId('project-1', 'page-123');
      
      expect(kvs.set).toHaveBeenCalledWith('confluence-page-project-project-1', 'page-123');
      expect(result).toBe(true);
    });

    it('should handle storage errors', async () => {
      kvs.set.mockRejectedValue(new Error('Storage error'));

      const result = await confluenceService.setConfiguredPageId('project-1', 'page-123');
      
      expect(result).toBe(false);
    });
  });

  describe('getConfiguredPageId', () => {
    it('should get configured page ID successfully', async () => {
      kvs.get.mockResolvedValue('page-123');

      const result = await confluenceService.getConfiguredPageId('project-1');
      
      expect(kvs.get).toHaveBeenCalledWith('confluence-page-project-project-1');
      expect(result).toBe('page-123');
    });

    it('should return null when no page is configured', async () => {
      kvs.get.mockResolvedValue(null);

      const result = await confluenceService.getConfiguredPageId('project-1');
      
      expect(result).toBeNull();
    });

    it('should handle storage errors', async () => {
      kvs.get.mockRejectedValue(new Error('Storage error'));

      const result = await confluenceService.getConfiguredPageId('project-1');
      
      expect(result).toBeNull();
    });
  });

  describe('isCacheValid', () => {
    it('should return true for valid cache', () => {
      const recentTime = Date.now() - 1000; // 1 second ago
      const cachedContent = {
        timestamp: recentTime,
        data: {}
      };

      const result = confluenceService.isCacheValid(cachedContent);
      expect(result).toBe(true);
    });

    it('should return false for expired cache', () => {
      const oldTime = Date.now() - (confluenceService.defaultCacheTTL + 1000);
      const cachedContent = {
        timestamp: oldTime,
        data: {}
      };

      const result = confluenceService.isCacheValid(cachedContent);
      expect(result).toBe(false);
    });

    it('should return false for cache without timestamp', () => {
      const cachedContent = { data: {} };

      const result = confluenceService.isCacheValid(cachedContent);
      expect(result).toBe(false);
    });

    it('should return false for null cache', () => {
      const result = confluenceService.isCacheValid(null);
      expect(result).toBe(false);
    });
  });

  describe('searchPagesByTitle', () => {
    it('should search pages by title successfully', async () => {
      const mockPages = [
        { id: '123', title: 'Test Page' }
      ];
      
      mockRequestConfluence.mockResolvedValue({
        ok: true,
        json: jest.fn().mockResolvedValue({ results: mockPages })
      });

      const result = await confluenceService.searchPagesByTitle('Test Page', 5);
      
      expect(mockRequestConfluence).toHaveBeenCalledWith('/wiki/api/v2/pages?title=Test%20Page&limit=5&expand=space,version');
      expect(result).toEqual(mockPages);
    });

    it('should use default limit when not specified', async () => {
      await confluenceService.searchPagesByTitle('Test');
      
      expect(mockRequestConfluence).toHaveBeenCalledWith('/wiki/api/v2/pages?title=Test&limit=10&expand=space,version');
    });

    it('should return empty array on API error', async () => {
      mockRequestConfluence.mockResolvedValue({
        ok: false,
        status: 404,
        statusText: 'Not Found'
      });

      const result = await confluenceService.searchPagesByTitle('Test');
      
      expect(result).toEqual([]);
    });
  });
});