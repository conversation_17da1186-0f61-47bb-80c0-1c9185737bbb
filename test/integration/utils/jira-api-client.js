/**
 * Jira API Client for Testing
 * 
 * This module provides functions for interacting with Jira REST API directly
 * using API tokens for local testing (outside of Forge runtime).
 */

import { config } from '../config.js';

/**
 * Make an authenticated request to the Jira REST API
 * @param {string} endpoint - The API endpoint (e.g., '/rest/api/3/field')
 * @param {Object} options - Request options (method, body, headers, etc.)
 * @returns {Promise<any>} The response data or null for empty responses
 */
export async function makeJiraRequest(endpoint, options = {}) {
  const url = new URL(endpoint, config.JIRA_BASE_URL);
  
  const requestOptions = {
    method: options.method || 'GET',
    headers: {
      'Authorization': `Basic ${Buffer.from(`${config.JIRA_EMAIL}:${config.JIRA_ACCESS_TOKEN}`).toString('base64')}`,
      'Accept': 'application/json',
      'Content-Type': 'application/json',
      ...options.headers
    }
  };

  if (options.body) {
    requestOptions.body = typeof options.body === 'string' ? options.body : JSON.stringify(options.body);
  }

  try {
    const response = await fetch(url.toString(), requestOptions);
    
    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`HTTP ${response.status}: ${response.statusText} - ${errorText}`);
    }

    // Handle empty responses (like from PUT requests)
    const contentType = response.headers.get('content-type');
    const contentLength = response.headers.get('content-length');
    
    // If there's no content or content-length is 0, return null
    if (contentLength === '0' || response.status === 204) {
      return null;
    }
    
    // If there's JSON content, parse it
    if (contentType && contentType.includes('application/json')) {
      const text = await response.text();
      return text ? JSON.parse(text) : null;
    }
    
    return null;
  } catch (error) {
    throw new Error(`Jira API request failed: ${error.message}`);
  }
}

/**
 * Get custom field ID(s) by field name(s) using createmeta API for context-specific field resolution
 * @param {string|string[]} fieldNames - Single field name or array of field names (e.g., 'Prompt' or ['Prompt', 'AI Requirements'])
 * @param {string} projectId - Project ID for context-specific field resolution
 * @param {string} issueTypeId - Issue type ID for context-specific field resolution
 * @returns {Promise<string|string[]>} The field ID(s) - single string if single input, array if array input
 */
export async function getCustomFieldId(fieldNames, projectId, issueTypeId) {
  const isArray = Array.isArray(fieldNames);
  const namesToProcess = isArray ? fieldNames : [fieldNames];
  
  try {
    const createmetaData = await makeJiraRequest(`/rest/api/3/issue/createmeta/${projectId}/issuetypes/${issueTypeId}`);
    const fieldsData = createmetaData.fields || [];
    const results = [];
    
    for (const fieldName of namesToProcess) {
      const foundField = fieldsData.find(f => f.name === fieldName);
      
      if (!foundField) {
        throw new Error(`Could not find custom field with name: ${fieldName} in project ${projectId}, issue type ${issueTypeId}`);
      }

      console.log(`Found field ${fieldName} with ID: ${foundField.fieldId}, Key: ${foundField.key}, Required: ${foundField.required}`);
      results.push(foundField.fieldId);
    }
    
    // Return single result or array based on input
    return isArray ? results : results[0];
  } catch (error) {
    throw new Error(`Error fetching custom field ID(s): ${error.message}`);
  }
}

/**
 * Get issue details
 * @param {string} issueId - The issue ID
 * @returns {Promise<Object>} The issue object
 */
export async function getIssue(issueId) {
  try {
    return await makeJiraRequest(`/rest/api/3/issue/${issueId}`);
  } catch (error) {
    throw new Error(`Error fetching issue ${issueId}: ${error.message}`);
  }
}

/**
 * Update issue fields
 * @param {string} issueId - The issue ID
 * @param {Object} fields - Object containing field IDs as keys and values to update
 * @returns {Promise<void>}
 */
export async function updateIssue(issueId, fields) {
  try {
    await makeJiraRequest(`/rest/api/3/issue/${issueId}`, {
      method: 'PUT',
      body: { fields }
    });
  } catch (error) {
    throw new Error(`Error updating issue ${issueId}: ${error.message}`);
  }
}

/**
 * Get current user information
 * @returns {Promise<Object>} The user object
 */
export async function getCurrentUser() {
  try {
    return await makeJiraRequest('/rest/api/3/myself');
  } catch (error) {
    throw new Error(`Error fetching current user: ${error.message}`);
  }
}

/**
 * Get all fields
 * @returns {Promise<Array>} Array of field objects
 */
export async function getAllFields() {
  try {
    return await makeJiraRequest('/rest/api/3/field');
  } catch (error) {
    throw new Error(`Error fetching fields: ${error.message}`);
  }
}

/**
 * Test connection to Jira API
 * @returns {Promise<Object>} Object with connection status and user info
 */
export async function testConnection() {
  try {
    const user = await getCurrentUser();
    return {
      success: true,
      user: {
        displayName: user.displayName,
        emailAddress: user.emailAddress,
        accountId: user.accountId
      }
    };
  } catch (error) {
    return {
      success: false,
      error: error.message
    };
  }
}
