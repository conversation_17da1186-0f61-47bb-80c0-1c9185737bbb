#!/usr/bin/env node

/**
 * Configuration Validation Script
 * 
 * This script validates that the Jira API configuration is set up correctly
 * and can connect to your Jira instance.
 * 
 * Usage:
 *   node test/validate-config.js
 */

import { config, validateConfig } from './config.js';
import { getCurrentUser, getIssue, getCustomFieldId } from './utils/jira-api-client.js';

async function validateConnection() {
  console.log('🔍 Validating Jira API connection...');

  try {
    // Test basic API access
    const myself = await getCurrentUser();
    console.log(`✅ Connected to <PERSON><PERSON> as: ${myself.displayName} (${myself.emailAddress})`);

    // Test issue access
    console.log(`🔍 Checking access to test issue ${config.TEST_ISSUE_ID}...`);
    const issue = await getIssue(config.TEST_ISSUE_ID);
    console.log(`✅ Can access issue: ${issue.key} - ${issue.fields.summary}`);

    // Test field access using createmeta API
    console.log('🔍 Checking custom fields...');
    const projectId = issue.fields.project.id;
    const issueTypeId = issue.fields.issuetype.id;
    
    try {
      const [simpleReqFieldId, fullReqFieldId] = await getCustomFieldId(['Prompt', 'AI Requirements'], projectId, issueTypeId);
      console.log(`✅ Found Prompt field: ${simpleReqFieldId}`);
      console.log(`✅ Found AI Requirements field: ${fullReqFieldId}`);
    } catch (error) {
      console.log('⚠️  One or more custom fields not found in the project/issue type context');
      console.log(`   Error: ${error.message}`);
    }
    
    console.log('');
    console.log('🎉 Configuration validation completed successfully!');
    console.log('   You can now run the integration tests.');
    
  } catch (error) {
    console.error('❌ Configuration validation failed:');
    console.error(`   ${error.message}`);
    console.error('');
    console.error('💡 Troubleshooting tips:');
    console.error('   - Check your JIRA_BASE_URL is correct');
    console.error('   - Verify your JIRA_ACCESS_TOKEN is valid');
    console.error('   - Ensure your JIRA_EMAIL matches the token owner');
    console.error('   - Make sure the test issue exists and you have access');
    process.exit(1);
  }
}

async function main() {
  console.log('🚀 Jira API Configuration Validator');
  console.log('');
  
  try {
    // Validate configuration
    validateConfig();
    console.log('');
    
    // Test connection
    await validateConnection();
    
  } catch (error) {
    console.error('❌ Validation failed:', error.message);
    process.exit(1);
  }
}

main().catch(console.error);
