# OpenRouter API Configuration
# Get your API key from https://openrouter.ai/
SECRET_OPENROUTER_API_KEY=your_openrouter_api_key_here

# Development/Testing Configuration
# Set to 'development' or 'production'
NODE_ENV=development

# AI Model Configuration (optional overrides)
# Default model: deepseek/deepseek-chat-v3-0324:free
# OPENROUTER_MODEL=deepseek/deepseek-chat-v3-0324:free
# OPENROUTER_TEMPERATURE=0.7
# OPENROUTER_MAX_TOKENS=1000

# Jira Configuration
# These are typically auto-configured by Forge but can be overridden for testing
# JIRA_BASE_URL=https://your-domain.atlassian.net