# Build Process Documentation

## Overview

This project uses a custom build process to generate the system prompt files from editable markdown sources.

## Build Process

### What It Does
- Reads `resources/system-prompt.md` (system instructions)
- Reads `resources/response-template.md` (response structure template)  
- Generates `build/system-prompt.js` with template substitution functions
- Creates a clean JavaScript module with `createFullPrompt()` function

### How It's Integrated

The build script automatically runs before:
- **Deployment**: `npm run deploy` (runs `predeploy` hook)
- **Testing**: `npm run test`, `npm run test:unit`, `npm run test:integration`
- **Manual Build**: `npm run build`

### Commands

```bash
# Manual build
npm run build

# Deploy (auto-builds first)
npm run deploy

# Test (auto-builds first)  
npm run test:unit
npm run test:integration
```

### Files

- **Source**: `resources/system-prompt.md`, `resources/response-template.md`
- **Generated**: `build/system-prompt.js` (gitignored)
- **Build Script**: `scripts/build-prompts.js`

### Template Variables

- `{{ISSUE_REQUIREMENTS}}` - Replaced with actual user requirements
- `{{RESPONSE_TEMPLATE}}` - Replaced with structured response format

### Forge Deployment

The current setup ensures:
✅ Build runs automatically before every deployment
✅ Build runs automatically before every test
✅ Generated files are excluded from version control
✅ Source markdown files are easy to edit

## Usage

1. Edit prompts in `resources/system-prompt.md`
2. Edit response structure in `resources/response-template.md`  
3. Run `npm run deploy` or `npm run test` - build happens automatically
4. Changes are immediately available in the deployed app