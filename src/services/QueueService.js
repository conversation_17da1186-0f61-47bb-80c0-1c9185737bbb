/**
 * Queue Service - Manages async event queues
 */

import { Queue } from '@forge/events';
import { config } from '../config/index.js';

export class QueueService {
  constructor() {
    this.queues = new Map();
  }

  /**
   * Get or create a queue instance
   * @param {string} queueKey - The queue key
   * @returns {Queue} Queue instance
   */
  getQueue(queueKey) {
    if (!this.queues.has(queueKey)) {
      this.queues.set(queueKey, new Queue({ key: queueKey }));
    }
    return this.queues.get(queueKey);
  }

  /**
   * Enqueue requirements processing job
   * @param {Object} jobData - The job data to enqueue
   * @param {Object} jobData.issue - Complete Jira issue object
   * @param {string} jobData.simpleRequirements - Prompt text
   * @param {string} jobData.fullReqFieldId - Full requirements field ID
   * @returns {Promise<string>} Job ID if successful
   */
  async enqueueRequirementsProcessing(jobData) {
    const { issue, simpleRequirements, fullReqFieldId } = jobData;

    try {
      const queue = this.getQueue(config.events.queues.requirements);
      
      const { jobId } = await queue.push([{
        body: {
          issue,
          simpleRequirements,
          fullReqFieldId
        }
      }]);
      
      console.log(`Enqueued processing for ${issue.key}`);
      return jobId;
    } catch (error) {
      console.error(`Failed to enqueue processing for ${issue.key}`);
      throw error;
    }
  }


}