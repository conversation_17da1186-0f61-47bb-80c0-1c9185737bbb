/**
 * AI Service - Handles AI-powered requirements expansion
 */

import { callOpenRouter } from '../utils/openrouter-client.js';
import { createFullPrompt } from '../../build/system-prompt.js';
import { SettingsService } from './SettingsService.js';
import { ConfluenceService } from './ConfluenceService.js';

export class AIService {
  constructor() {
    this.settingsService = new SettingsService();
    this.confluenceService = new ConfluenceService();
  }
  
  /**
   * Expand simple requirements using AI
   * @param {string} issueTitle - The Jira issue title/summary
   * @param {string} simpleRequirements - The prompt text
   * @param {string} issueType - The issue type (story, task, bug) - optional for backward compatibility
   * @param {string} projectId - Project ID for project-scoped settings
   * @param {Object} options - Optional configuration overrides
   * @returns {Promise<string|null>} Expanded requirements or null if failed
   */
  async expandRequirements(issueTitle, simpleRequirements, issueType = null, projectId = null, options = {}) {
    try {
      if (!projectId) {
        throw new Error('Project ID is required for AI expansion');
      }
      
      const customTemplate = issueType 
        ? await this.settingsService.getTemplateForIssueType(issueType, projectId)
        : await this.settingsService.getTemplateForIssueType('story', projectId);
      
      const aiConfig = await this.settingsService.getAIConfig(projectId);
      
      // Fetch project context from Confluence if configured (gracefully handle failures)
      let projectContext = '';
      try {
        projectContext = await this.confluenceService.getProjectContext(projectId);
      } catch (error) {
        console.warn('Failed to fetch Confluence project context, continuing without it:', error.message);
        projectContext = '';
      }
      
      const finalOptions = {
        model: aiConfig.model,
        temperature: aiConfig.temperature,
        maxTokens: aiConfig.maxTokens,
        apiEndpoint: aiConfig.apiEndpoint,
        ...options
      };
      
      const prompt = createFullPrompt(issueTitle, simpleRequirements, customTemplate, projectContext);
      const result = await callOpenRouter(prompt, finalOptions);
      
      if (!result) {
        console.error('AI service returned null response');
        return null;
      }

      return result;
    } catch (error) {
      console.error('Error in AI service expansion:', error);
      throw error;
    }
  }
}