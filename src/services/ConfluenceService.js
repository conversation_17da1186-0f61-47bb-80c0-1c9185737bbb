/**
 * Confluence Service - Handles Confluence API operations and content caching
 */

import api, { route } from '@forge/api';
import { kvs } from '@forge/kvs';
import { config } from '../config/index.js';

export class ConfluenceService {
  constructor() {
    this.cachePrefix = 'confluence-cache-';
    this.defaultCacheTTL = config.confluence.cacheTTL;
  }

  /**
   * Get project-scoped cache key
   * @param {string} baseKey - Base cache key
   * @param {string} projectId - Project ID for scoping
   * @returns {string} Project-scoped cache key
   */
  getCacheKey(baseKey, projectId) {
    return `${this.cachePrefix}project-${projectId}-${baseKey}`;
  }

  /**
   * Get all available Confluence spaces
   * @returns {Promise<Array>} Array of space objects
   */
  async getSpaces() {
    try {
      const response = await api.asApp().requestConfluence(route`/wiki/api/v2/spaces`);
      
      if (!response.ok) {
        console.error(`Failed to fetch Confluence spaces: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();
      return data.results || [];
    } catch (error) {
      console.error('Error fetching Confluence spaces:', error);
      return [];
    }
  }

  /**
   * Get pages in a specific space
   * @param {string} spaceKey - Space key to search in
   * @param {number} limit - Maximum number of pages to return
   * @returns {Promise<Array>} Array of page objects
   */
  async getPagesInSpace(spaceKey, limit = 25) {
    try {
      const response = await api.asApp().requestConfluence(
        route`/wiki/api/v2/spaces/${spaceKey}/pages?limit=${limit}&expand=version,body.view`
      );
      
      if (!response.ok) {
        console.error(`Failed to fetch pages in space ${spaceKey}: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();
      return data.results || [];
    } catch (error) {
      console.error(`Error fetching pages in space ${spaceKey}:`, error);
      return [];
    }
  }

  /**
   * Get a specific page by ID with content
   * @param {string} pageId - Page ID to fetch
   * @param {string} projectId - Project ID for caching scope
   * @param {boolean} useCache - Whether to use cached content
   * @returns {Promise<Object|null>} Page object with content or null if not found
   */
  async getPageContent(pageId, projectId, useCache = true) {
    try {
      // Check cache first if enabled
      if (useCache) {
        const cacheKey = this.getCacheKey(`page-${pageId}`, projectId);
        const cachedContent = await kvs.get(cacheKey);
        
        if (cachedContent && this.isCacheValid(cachedContent)) {
          console.log(`Using cached content for page ${pageId}`);
          return cachedContent.data;
        }
      }

      // Fetch from Confluence API
      const response = await api.asApp().requestConfluence(
        route`/wiki/api/v2/pages/${pageId}?expand=version,body.view,space`
      );
      
      if (!response.ok) {
        console.error(`Failed to fetch page ${pageId}: ${response.status} ${response.statusText}`);
        return null;
      }

      const pageData = await response.json();
      
      // Cache the result if caching is enabled
      if (useCache) {
        await this.cachePageContent(pageId, pageData, projectId);
      }
      
      return pageData;
    } catch (error) {
      console.error(`Error fetching page content for ${pageId}:`, error);
      return null;
    }
  }

  /**
   * Extract plain text content from Confluence page
   * @param {Object} pageData - Page data from Confluence API
   * @returns {string} Plain text content
   */
  extractPlainTextContent(pageData) {
    try {
      if (!pageData || !pageData.body || !pageData.body.view) {
        return '';
      }

      const htmlContent = pageData.body.view.value;
      
      // Basic HTML to plain text conversion
      // Remove HTML tags but preserve line breaks
      const plainText = htmlContent
        .replace(/<br\s*\/?>/gi, '\n')
        .replace(/<\/p>/gi, '\n\n')
        .replace(/<\/div>/gi, '\n')
        .replace(/<\/h[1-6]>/gi, '\n\n')
        .replace(/<[^>]*>/g, '')
        .replace(/&nbsp;/g, ' ')
        .replace(/&amp;/g, '&')
        .replace(/&lt;/g, '<')
        .replace(/&gt;/g, '>')
        .replace(/&quot;/g, '"')
        .trim();

      return plainText;
    } catch (error) {
      console.error('Error extracting plain text from page:', error);
      return '';
    }
  }

  /**
   * Get project context from configured Confluence page
   * @param {string} projectId - Project ID
   * @returns {Promise<string>} Project context text or empty string
   */
  async getProjectContext(projectId) {
    try {
      // Get configured page ID for this project
      const pageId = await this.getConfiguredPageId(projectId);
      
      if (!pageId) {
        console.log(`No Confluence page configured for project ${projectId}`);
        return '';
      }

      // Fetch page content
      const pageData = await this.getPageContent(pageId, projectId);
      
      if (!pageData) {
        console.error(`Failed to fetch configured page ${pageId} for project ${projectId}`);
        return '';
      }

      // Extract plain text
      const context = this.extractPlainTextContent(pageData);
      
      console.log(`Retrieved ${context.length} characters of context from page ${pageId}`);
      return context;
    } catch (error) {
      console.error(`Error getting project context for ${projectId}:`, error);
      return '';
    }
  }

  /**
   * Set the configured Confluence page for a project
   * @param {string} projectId - Project ID
   * @param {string} pageId - Page ID to configure
   * @returns {Promise<boolean>} Success status
   */
  async setConfiguredPageId(projectId, pageId) {
    try {
      const storageKey = `confluence-page-project-${projectId}`;
      await kvs.set(storageKey, pageId);
      
      // Clear any cached content for this project when configuration changes
      await this.clearProjectCache(projectId);
      
      return true;
    } catch (error) {
      console.error(`Error setting configured page for project ${projectId}:`, error);
      return false;
    }
  }

  /**
   * Get the configured Confluence page ID for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<string|null>} Configured page ID or null
   */
  async getConfiguredPageId(projectId) {
    try {
      const storageKey = `confluence-page-project-${projectId}`;
      const pageId = await kvs.get(storageKey);
      return pageId || null;
    } catch (error) {
      console.error(`Error getting configured page for project ${projectId}:`, error);
      return null;
    }
  }

  /**
   * Cache page content with TTL
   * @param {string} pageId - Page ID
   * @param {Object} pageData - Page data to cache
   * @param {string} projectId - Project ID for scoping
   * @returns {Promise<void>}
   */
  async cachePageContent(pageId, pageData, projectId) {
    try {
      const cacheKey = this.getCacheKey(`page-${pageId}`, projectId);
      const cacheData = {
        data: pageData,
        timestamp: Date.now(),
        version: pageData.version?.number || 0
      };
      
      await kvs.set(cacheKey, cacheData);
      console.log(`Cached content for page ${pageId}`);
    } catch (error) {
      console.error(`Error caching page content for ${pageId}:`, error);
    }
  }

  /**
   * Check if cached content is still valid
   * @param {Object} cachedContent - Cached content object
   * @returns {boolean} Whether cache is valid
   */
  isCacheValid(cachedContent) {
    if (!cachedContent || !cachedContent.timestamp) {
      return false;
    }
    
    const now = Date.now();
    const cacheAge = now - cachedContent.timestamp;
    
    return cacheAge < this.defaultCacheTTL;
  }

  /**
   * Clear all cached content for a project
   * @param {string} projectId - Project ID
   * @returns {Promise<void>}
   */
  async clearProjectCache(projectId) {
    try {
      // Note: In a real implementation, you'd want to query and delete all keys
      // with the project prefix. For now, we'll just log the intent.
      console.log(`Clearing Confluence cache for project ${projectId}`);
      
      // The kvs doesn't support bulk delete by prefix directly,
      // so we'd need to track cache keys separately or implement
      // a more sophisticated cache invalidation strategy
    } catch (error) {
      console.error(`Error clearing cache for project ${projectId}:`, error);
    }
  }

  /**
   * Search for pages by title across all spaces
   * @param {string} title - Page title to search for
   * @param {number} limit - Maximum results to return
   * @returns {Promise<Array>} Array of matching pages
   */
  async searchPagesByTitle(title, limit = 10) {
    try {
      const response = await api.asApp().requestConfluence(
        route`/wiki/api/v2/pages?title=${encodeURIComponent(title)}&limit=${limit}&expand=space,version`
      );
      
      if (!response.ok) {
        console.error(`Failed to search pages by title: ${response.status} ${response.statusText}`);
        return [];
      }

      const data = await response.json();
      return data.results || [];
    } catch (error) {
      console.error('Error searching pages by title:', error);
      return [];
    }
  }
}