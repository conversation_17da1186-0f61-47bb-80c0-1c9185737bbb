import { RequirementsService, JiraService, QueueService, AIService } from '../services/index.js';
import { config } from '../config/index.js';

export const promptUpdateHandler = async (event, context) => {
  const issueId = event.issue.id;
  const issueKey = event.issue.key;
  const isCreation = !event.changelog || !event.changelog.items || event.changelog.items.length === 0;

  console.log(`Processing ${isCreation ? 'creation' : 'update'} for issue ${issueKey}`);

  const jiraService = new JiraService();
  const queueService = new QueueService();

  const issue = await jiraService.getIssue(issueId);
  if (!issue || !issue.fields) {
    console.error(`Could not fetch issue details for ${issueKey}`);
    return;
  }

  const projectId = issue.fields.project.id;
  const issueTypeId = issue.fields.issuetype.id;

  const [simpleReqField, fullReqField, summaryField] = await jiraService.getFieldsByName([
    config.fields.prompt,
    config.fields.aiRequirements,
    config.fields.summary
  ], projectId, issueTypeId);

  if (!simpleReqField || !fullReqField || !summaryField) {
    console.error('Could not resolve required fields');
    return;
  }

  if (!isCreation && !jiraService.isFieldUpdated(event.changelog, simpleReqField)) {
    return;
  }

  const issueSimpleRequirements = jiraService.extractFieldValueFromIssue(issue, simpleReqField);
  if (!issueSimpleRequirements) {
    return;
  }

  const issueTitle = jiraService.extractFieldValueFromIssue(issue, summaryField) || '';

  try {
    const jobId = await queueService.enqueueRequirementsProcessing({
      issue,
      simpleRequirements: issueSimpleRequirements,
      fullReqFieldId: fullReqField.id
    });
  } catch (error) {
    console.error(`Failed to enqueue processing for ${issueKey}:`, error);
  }
};

export const requirementsProcessorHandler = async (event) => {
  const aiService = new AIService();
  const requirementsService = new RequirementsService();
  const jiraService = new JiraService();

  const { issue, simpleRequirements, fullReqFieldId } = event.call.payload.body;
  const issueId = issue.id;
  const issueKey = issue.key;
  const issueTitle = issue.fields.summary;
  const issueType = issue.fields.issuetype?.name?.toLowerCase() || 'story';
  const projectId = issue.fields.project?.id;

  console.log(`Processing AI expansion for ${issueKey} (${issueType})`);

  try {
    const rawRequirements = await aiService.expandRequirements(issueTitle, simpleRequirements, issueType, projectId);
    
    if (!rawRequirements) {
      throw new Error('AI service failed to expand requirements');
    }

    const adfContent = requirementsService.processRequirements(rawRequirements);

    const updateSuccess = await jiraService.updateIssueFields(issueId, {
      [fullReqFieldId]: adfContent
    });

    if (!updateSuccess) {
      throw new Error('Failed to update Jira issue with expanded requirements');
    }

    console.log(`Updated AI Requirements field for ${issueKey}`);
  } catch (error) {
    console.error(`Error processing requirements for ${issueKey}:`, error);
    throw error;
  }
};
