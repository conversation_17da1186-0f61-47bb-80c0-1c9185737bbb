### User Story
As a {type of user}, I want {an action or feature}, so that {benefit/value}.

### Background & Context
{Brief description of what this feature will accomplish and why it's needed.}

### Acceptance Criteria {add as many as appropriate}
- [ ] {Acceptance Criteria}

### Dependencies {only add dependencies that are applicable}
- {List any other user stories, tasks, or technical considerations this story depends on}
- {Integration points with other systems}
- {External dependencies or third-party services}

### Technical Considerations {only add considerations that are applicable}
- {Architecture and design decisions}
- {Data model changes}
- {API changes or additions}
- {Performance implications}
- {Security considerations}
- {Constraints or limitations}

### Notes & Additional Information {only add points if needed for context}
- {Mockups/designs}
- {Business rules or edge cases}
- {Future enhancements or follow-up stories}