### Task Description
{Clear description of what needs to be accomplished}

### Objectives
- [ ] {Primary objective or deliverable}
- [ ] {Secondary objectives if applicable}

### Scope & Requirements
{Define what is included and excluded from this task}

### Deliverables
- {What will be produced or completed}
- {Documentation or artifacts to be created}
- {Code, configurations, or other outputs}

### Acceptance Criteria
- [ ] {Specific criteria that define completion}
- [ ] {Quality standards or validation requirements}

### Dependencies {only add dependencies that are applicable}
- {Other tasks that must be completed first}
- {Resources or access required}
- {External dependencies or approvals needed}

### Technical Considerations {only add considerations that are applicable}
- {Implementation approach or methodology}
- {Tools, frameworks, or technologies to be used}
- {Performance or security considerations}
- {Integration points or system impacts}