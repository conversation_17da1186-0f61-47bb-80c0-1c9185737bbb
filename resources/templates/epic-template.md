### Epic Overview
{High-level description of the epic and its strategic value}

### Epic Goals
- [ ] {Primary goal or outcome}
- [ ] {Secondary goals if applicable}

### User Stories & Features
{List of user stories and features that comprise this epic}

### Success Criteria
- [ ] {How will success be measured}
- [ ] {Key performance indicators}

### Dependencies & Prerequisites
- {Other epics or initiatives this depends on}
- {Technical prerequisites or infrastructure needs}

### Timeline & Milestones
{Key milestones and delivery expectations}

### Acceptance Criteria
- [ ] {Epic-level acceptance criteria}
- [ ] {Business value delivery confirmation}

### Technical Considerations
- {Architecture implications}
- {Technical debt or infrastructure changes}
- {Integration points with other systems}

### Notes & Additional Information
- {Strategic context or business rationale}
- {Stakeholder information}
- {Future enhancements or follow-up epics}