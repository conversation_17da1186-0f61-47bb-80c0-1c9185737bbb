{"name": "admin-page", "version": "1.0.0", "private": true, "homepage": "./", "dependencies": {"@atlaskit/tokens": "^5.5.0", "@forge/bridge": "^5.2.0", "@uiw/react-md-editor": "^4.0.8", "react": "^18.2.0", "react-dom": "^18.2.0"}, "scripts": {"build": "webpack --mode production", "dev": "webpack serve --mode development"}, "devDependencies": {"@babel/core": "^7.22.0", "@babel/preset-env": "^7.22.0", "@babel/preset-react": "^7.22.0", "babel-loader": "^9.1.0", "css-loader": "^6.8.0", "html-webpack-plugin": "^5.5.0", "mini-css-extract-plugin": "^2.7.0", "webpack": "^5.88.0", "webpack-cli": "^5.1.0", "webpack-dev-server": "^4.15.0"}}