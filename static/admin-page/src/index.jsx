import React from 'react';
import { createRoot } from 'react-dom/client';
import { invoke, view } from '@forge/bridge';
import MDEditor from '@uiw/react-md-editor';
import './styles.css';


function AdminApp() {
    const [activeTab, setActiveTab] = React.useState('templates');
    
    return (
        <div className="admin-container">
            <TabNavigation activeTab={activeTab} onTabChange={setActiveTab} />
            {activeTab === 'settings' && <SettingsView />}
            {activeTab === 'templates' && <TemplatesView />}
            {activeTab === 'confluence' && <ConfluenceView />}
            <StatusMessage />
        </div>
    );
}

function TabNavigation({ activeTab, onTabChange }) {
    const tabs = [
        { id: 'templates', label: 'Templates', description: 'Edit issue type templates' },
        { id: 'settings', label: 'Settings', description: 'Configure AI settings' },
        { id: 'confluence', label: 'Project Context', description: 'Configure Confluence project context' }
    ];
    
    return (
        <div className="tab-navigation">
            <div className="tab-list">
                {tabs.map(tab => (
                    <button
                        key={tab.id}
                        className={`tab-button ${activeTab === tab.id ? 'active' : ''}`}
                        onClick={() => onTabChange(tab.id)}
                        aria-selected={activeTab === tab.id}
                    >
                        {tab.label}
                    </button>
                ))}
            </div>
        </div>
    );
}

function TemplatesView() {
    const [currentIssueType, setCurrentIssueType] = React.useState('story');
    const [templates, setTemplates] = React.useState({});
    const [currentTemplate, setCurrentTemplate] = React.useState('');
    const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(true);
    const [availableIssueTypes, setAvailableIssueTypes] = React.useState([]);
    const [confirmModal, setConfirmModal] = React.useState({ isOpen: false, type: '', data: null });
    
    // Load project context and issue types on component mount
    React.useEffect(() => {
        loadProjectContext();
    }, []);
    
    const loadProjectContext = async () => {
        setIsLoading(true);
        
        try {
            const contextResponse = await invoke('admin-resolver', {
                method: 'getCurrentProjectContext'
            });
            
            if (!contextResponse?.success || !contextResponse?.projectId) {
                throw new Error('Could not determine project context');
            }
            
            await loadProjectIssueTypes(contextResponse.projectId);
            
        } catch (error) {
            console.error('Failed to load project context:', error.message);
            setAvailableIssueTypes([]);
            setIsLoading(false);
        }
    };
    
    const loadProjectIssueTypes = async (projectId) => {
        try {
            const response = await invoke('admin-resolver', {
                method: 'getProjectIssueTypes',
                payload: { projectId }
            });
            
            if (response && response.success && response.issueTypes) {
                // Sort issue types in specific order
                const priorityOrder = ['story', 'bug', 'task', 'subtask', 'epic'];
                const sortedIssueTypes = response.issueTypes.sort((a, b) => {
                    const aName = a.name.toLowerCase();
                    const bName = b.name.toLowerCase();
                    
                    const aIndex = priorityOrder.indexOf(aName);
                    const bIndex = priorityOrder.indexOf(bName);
                    
                    if (aIndex !== -1 && bIndex !== -1) {
                        return aIndex - bIndex;
                    }
                    
                    if (aIndex !== -1) return -1;
                    if (bIndex !== -1) return 1;
                    
                    return a.name.localeCompare(b.name);
                });
                
                setAvailableIssueTypes(sortedIssueTypes);
                
                const issueTypeNames = sortedIssueTypes.map(it => it.name);
                await loadTemplatesForIssueTypes(issueTypeNames);
            } else {
                throw new Error('Failed to load issue types');
            }
        } catch (error) {
            console.error('Failed to load issue types:', error);
            throw error;
        }
    };
    
    const loadTemplatesForIssueTypes = async (issueTypeNames) => {
        try {
            const response = await invoke('admin-resolver', {
                method: 'getTemplatesForIssueTypes',
                payload: { issueTypes: issueTypeNames }
            });
            
            if (response && response.success && response.templates) {
                setTemplates(response.templates);
                
                const firstIssueType = issueTypeNames.length > 0 ? issueTypeNames[0] : '';
                const firstTemplate = response.templates[firstIssueType] || '';
                
                setCurrentIssueType(firstIssueType);
                setCurrentTemplate(firstTemplate);
            } else {
                setCurrentTemplate('');
            }
        } catch (error) {
            console.error('Failed to load templates:', error);
            setCurrentTemplate('');
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleIssueTypeChange = (newIssueType) => {
        if (hasUnsavedChanges) {
            const userConfirmed = window.confirm(
                'You have unsaved changes that will be lost if you switch issue types. Do you want to continue?'
            );
            
            if (!userConfirmed) {
                return;
            }
        }
        
        // Save current template to memory before switching
        const updatedTemplates = { ...templates };
        updatedTemplates[currentIssueType] = currentTemplate;
        setTemplates(updatedTemplates);
        
        // Switch to new issue type
        setCurrentIssueType(newIssueType);
        setCurrentTemplate(templates[newIssueType] || '');
        setHasUnsavedChanges(false);
    };
    
    const handleTemplateChange = (value) => {
        setCurrentTemplate(value);
        if (!hasUnsavedChanges) {
            setHasUnsavedChanges(true);
        }
    };
    
    const handleSave = async () => {
        const template = currentTemplate.trim();
        
        if (!template) {
            showStatus('error', '❌ Template cannot be empty');
            return;
        }
        
        showStatus('info', `💾 Saving ${currentIssueType} template...`);
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'saveTemplateByType',
                payload: {
                    issueType: currentIssueType,
                    template: template
                }
            });
            
            if (response && response.success) {
                const updatedTemplates = { ...templates };
                updatedTemplates[currentIssueType] = template;
                setTemplates(updatedTemplates);
                setHasUnsavedChanges(false);
                showStatus('success', `✅ ${currentIssueType} template saved successfully!`);
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to save template:', error);
            showStatus('error', `❌ Error saving template: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleReset = () => {
        setConfirmModal({
            isOpen: true,
            type: 'resetCurrent',
            data: { issueType: currentIssueType }
        });
    };
    
    const performResetCurrent = async (issueType) => {
        showStatus('info', `🔄 Resetting ${issueType} template to default...`);
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'resetTemplateToDefault',
                payload: { issueType }
            });
            
            if (response && response.success && response.template !== undefined) {
                setCurrentTemplate(response.template);
                
                const updatedTemplates = { ...templates };
                updatedTemplates[issueType] = response.template;
                setTemplates(updatedTemplates);
                
                setHasUnsavedChanges(false);
                showStatus('success', `✅ ${issueType} template reset to default!`);
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to reset template:', error);
            showStatus('error', `❌ Error resetting template: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleResetAll = () => {
        setConfirmModal({
            isOpen: true,
            type: 'resetAll',
            data: {}
        });
    };
    
    const performResetAll = async () => {
        showStatus('info', '🔄 Resetting all templates to defaults...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'resetTemplatesToDefaults'
            });
            
            if (response && response.success) {
                setTemplates({});
                
                const issueTypeNames = availableIssueTypes.map(it => it.name);
                await loadTemplatesForIssueTypes(issueTypeNames);
                
                showStatus('success', '✅ All templates reset to defaults successfully!');
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to reset all templates:', error);
            showStatus('error', `❌ Error resetting templates: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleConfirmModal = async () => {
        const modalType = confirmModal.type;
        const modalData = confirmModal.data;
        
        setConfirmModal({ isOpen: false, type: '', data: null });
        
        try {
            if (modalType === 'resetCurrent') {
                await performResetCurrent(modalData.issueType);
            } else if (modalType === 'resetAll') {
                await performResetAll();
            }
        } catch (error) {
            console.error('Modal operation failed:', error);
            showStatus('error', `❌ Operation failed: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleCancelModal = () => {
        setConfirmModal({ isOpen: false, type: '', data: null });
    };
    
    if (isLoading) {
        return (
            <div className="template-card">
                <div className="loading-state">
                    <p>Loading templates...</p>
                </div>
            </div>
        );
    }
    
    return (
        <div className="template-card">
            <TemplateHeader 
                currentIssueType={currentIssueType}
                onIssueTypeChange={handleIssueTypeChange}
                availableIssueTypes={availableIssueTypes}
            />
            
            <p className="card-description">Define the structure for AI-expanded requirements. Use {'{curly brackets}'} for placeholders or instructions.</p>
            
            <TemplateEditor 
                value={currentTemplate}
                onChange={handleTemplateChange}
            />
            
            {hasUnsavedChanges && (
                <div className="unsaved-warning">
                    ⚠️ You have unsaved changes that will be lost if you switch issue types.
                </div>
            )}
            
            <ButtonGroup 
                onSave={handleSave}
                onReset={handleReset}
                onResetAll={handleResetAll}
                hasUnsavedChanges={hasUnsavedChanges}
            />
            
            <ConfirmationModal
                isOpen={confirmModal.isOpen}
                onClose={handleCancelModal}
                onConfirm={handleConfirmModal}
                title={
                    confirmModal.type === 'resetCurrent' 
                        ? `Reset ${confirmModal.data?.issueType || ''} Template`
                        : 'Reset All Templates'
                }
                message={
                    confirmModal.type === 'resetCurrent'
                        ? `Are you sure you want to reset the ${confirmModal.data?.issueType || ''} template to default? This action cannot be undone.`
                        : 'Are you sure you want to reset ALL templates to defaults? This will clear all customized templates, even for issue types that may have been removed from your project. This action cannot be undone.'
                }
                confirmText={
                    confirmModal.type === 'resetCurrent' ? 'Reset Template' : 'Reset All Templates'
                }
                cancelText="Cancel"
                isDangerous={true}
            />
        </div>
    );
}

function SettingsView() {
    const [settings, setSettings] = React.useState({
        aiModel: '',
        temperature: '0.7',
        maxTokens: '1000'
    });
    const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false);
    const [isLoading, setIsLoading] = React.useState(true);
    
    // Load settings on component mount
    React.useEffect(() => {
        loadSettings();
    }, []);
    
    const loadSettings = async () => {
        setIsLoading(true);
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'getSettings'
            });
            
            if (response && response.success && response.settings) {
                setSettings(response.settings);
            } else {
                showStatus('info', '💡 Using default settings');
            }
        } catch (error) {
            console.error('Failed to load settings:', error);
            showStatus('error', '❌ Error loading settings');
        } finally {
            setIsLoading(false);
        }
    };
    
    const handleSettingsChange = (field, value) => {
        setSettings(prev => ({
            ...prev,
            [field]: value
        }));
        
        if (!hasUnsavedChanges) {
            setHasUnsavedChanges(true);
        }
    };
    
    const handleSave = async () => {
        showStatus('info', '💾 Saving settings...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'saveSettings',
                payload: {
                    settings: settings
                }
            });
            
            if (response && response.success) {
                setHasUnsavedChanges(false);
                showStatus('success', '✅ Settings saved successfully!');
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to save settings:', error);
            showStatus('error', `❌ Error saving settings: ${error.message || 'Unknown error'}`);
        }
    };
    
    const handleReset = async () => {
        const confirmReset = window.confirm('Are you sure you want to reset all settings to defaults? This action cannot be undone.');
        if (!confirmReset) return;
        
        showStatus('info', '🔄 Resetting settings to defaults...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'resetToDefaults'
            });
            
            if (response && response.success && response.settings) {
                setSettings(response.settings);
                setHasUnsavedChanges(false);
                showStatus('success', '✅ Settings reset to defaults successfully!');
            } else {
                showStatus('error', `❌ ${response?.error || 'Unknown error'}`);
            }
        } catch (error) {
            console.error('Failed to reset settings:', error);
            showStatus('error', `❌ Error resetting settings: ${error.message || 'Unknown error'}`);
        }
    };
    
    if (isLoading) {
        return (
            <div className="settings-card">
                <div className="loading-state">
                    <p>Loading settings...</p>
                </div>
            </div>
        );
    }
    
    return (
        <div className="settings-card">
            <div className="settings-header">
                <h2 className="card-title">AI Configuration</h2>
                <p className="card-description">Configure the AI model and parameters used for requirements expansion.</p>
            </div>
            
            <div className="settings-form">
                <div className="form-group">
                    <label htmlFor="aiModel" className="form-label">AI Model</label>
                    <input
                        type="text"
                        id="aiModel"
                        className="form-input"
                        value={settings.aiModel}
                        onChange={(e) => handleSettingsChange('aiModel', e.target.value)}
                        placeholder="e.g., deepseek/deepseek-chat-v3-0324:free"
                    />
                    <p className="form-help">The AI model identifier used for requirements expansion.</p>
                </div>
                
                <div className="form-group">
                    <label htmlFor="temperature" className="form-label">Temperature</label>
                    <div className="temperature-control">
                        <input
                            type="range"
                            id="temperature"
                            className="form-range"
                            min="0"
                            max="2"
                            step="0.1"
                            value={settings.temperature}
                            onChange={(e) => handleSettingsChange('temperature', e.target.value)}
                        />
                        <input
                            type="number"
                            className="form-input temperature-input"
                            min="0"
                            max="2"
                            step="0.1"
                            value={settings.temperature}
                            onChange={(e) => handleSettingsChange('temperature', e.target.value)}
                        />
                    </div>
                    <p className="form-help">Controls randomness: lower values = more focused, higher values = more creative.</p>
                </div>
                
                <div className="form-group">
                    <label htmlFor="maxTokens" className="form-label">Max Tokens</label>
                    <input
                        type="number"
                        id="maxTokens"
                        className="form-input"
                        min="1"
                        max="4000"
                        value={settings.maxTokens}
                        onChange={(e) => handleSettingsChange('maxTokens', e.target.value)}
                    />
                    <p className="form-help">Maximum number of tokens in the AI response (1-4000).</p>
                </div>
            </div>
            
            {hasUnsavedChanges && (
                <div className="unsaved-warning">
                    ⚠️ You have unsaved changes that will be lost if you switch tabs.
                </div>
            )}
            
            <div className="button-group">
                <button 
                    className="btn-primary"
                    onClick={handleSave}
                >
                    {hasUnsavedChanges ? 'Save Settings *' : 'Save Settings'}
                </button>
                <button 
                    className="btn-secondary"
                    onClick={handleReset}
                >
                    Reset to Defaults
                </button>
            </div>
        </div>
    );
}

function ConfluenceView() {
    const [spaces, setSpaces] = React.useState([]);
    const [pages, setPages] = React.useState([]);
    const [selectedSpaceKey, setSelectedSpaceKey] = React.useState('');
    const [selectedPageId, setSelectedPageId] = React.useState('');
    const [currentPageConfig, setCurrentPageConfig] = React.useState('');
    const [isLoading, setIsLoading] = React.useState(true);
    const [hasUnsavedChanges, setHasUnsavedChanges] = React.useState(false);
    
    // Load initial data
    React.useEffect(() => {
        loadConfluenceData();
    }, []);
    
    const loadConfluenceData = async () => {
        setIsLoading(true);
        
        try {
            // Load spaces and current configuration in parallel
            const [spacesResponse, configResponse] = await Promise.all([
                invoke('admin-resolver', { method: 'getConfluenceSpaces' }),
                invoke('admin-resolver', { method: 'getConfluencePageConfig' })
            ]);
            
            if (spacesResponse && spacesResponse.success) {
                setSpaces(spacesResponse.spaces);
            }
            
            if (configResponse && configResponse.success && configResponse.pageId) {
                setCurrentPageConfig(configResponse.pageId);
                setSelectedPageId(configResponse.pageId);
            }
        } catch (error) {
            console.error('Failed to load Confluence data:', error);
            showStatus('error', '❌ Error loading Confluence data');
        } finally {
            setIsLoading(false);
        }
    };
    
    const loadPagesForSpace = async (spaceKey) => {
        if (!spaceKey) {
            setPages([]);
            return;
        }
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'getConfluencePages',
                payload: { spaceKey, limit: 50 }
            });
            
            if (response && response.success) {
                setPages(response.pages);
            } else {
                setPages([]);
                showStatus('error', '❌ Failed to load pages for this space');
            }
        } catch (error) {
            console.error('Failed to load pages:', error);
            setPages([]);
            showStatus('error', '❌ Error loading pages');
        }
    };
    
    const handleSpaceChange = (spaceKey) => {
        setSelectedSpaceKey(spaceKey);
        setSelectedPageId('');
        setHasUnsavedChanges(true);
        loadPagesForSpace(spaceKey);
    };
    
    const handlePageChange = (pageId) => {
        setSelectedPageId(pageId);
        if (pageId !== currentPageConfig) {
            setHasUnsavedChanges(true);
        } else {
            setHasUnsavedChanges(false);
        }
    };
    
    const handleSave = async () => {
        if (!selectedPageId) {
            showStatus('error', '❌ Please select a page first');
            return;
        }
        
        showStatus('info', '💾 Saving Confluence configuration...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'setConfluencePageConfig',
                payload: { pageId: selectedPageId }
            });
            
            if (response && response.success) {
                setCurrentPageConfig(selectedPageId);
                setHasUnsavedChanges(false);
                showStatus('success', '✅ Confluence configuration saved successfully');
            } else {
                showStatus('error', `❌ ${response.error || 'Failed to save configuration'}`);
            }
        } catch (error) {
            console.error('Failed to save configuration:', error);
            showStatus('error', '❌ Error saving configuration');
        }
    };
    
    const handleClear = async () => {
        showStatus('info', '🗑️ Clearing Confluence configuration...');
        
        try {
            const response = await invoke('admin-resolver', {
                method: 'setConfluencePageConfig',
                payload: { pageId: null }
            });
            
            if (response && response.success) {
                setCurrentPageConfig('');
                setSelectedPageId('');
                setSelectedSpaceKey('');
                setPages([]);
                setHasUnsavedChanges(false);
                showStatus('success', '✅ Confluence configuration cleared');
            } else {
                showStatus('error', `❌ ${response.error || 'Failed to clear configuration'}`);
            }
        } catch (error) {
            console.error('Failed to clear configuration:', error);
            showStatus('error', '❌ Error clearing configuration');
        }
    };
    
    if (isLoading) {
        return (
            <div className="settings-card">
                <div className="loading-state">
                    <p>Loading Confluence data...</p>
                </div>
            </div>
        );
    }
    
    return (
        <div className="settings-card">
            <div className="settings-header">
                <h2 className="card-title">Project Context Configuration</h2>
                <p className="card-description">
                    Select a Confluence page that contains project context information. 
                    This content will be included in AI prompts to provide better, more relevant requirements expansion.
                </p>
            </div>
            
            <div className="settings-form">
                <div className="form-group">
                    <label htmlFor="confluenceSpace" className="form-label">Confluence Space</label>
                    <select
                        id="confluenceSpace"
                        className="form-select"
                        value={selectedSpaceKey}
                        onChange={(e) => handleSpaceChange(e.target.value)}
                    >
                        <option value="">Select a space...</option>
                        {spaces.map(space => (
                            <option key={space.key} value={space.key}>
                                {space.name} ({space.key})
                            </option>
                        ))}
                    </select>
                    <p className="form-help">Choose the Confluence space containing your project context page.</p>
                </div>
                
                {selectedSpaceKey && (
                    <div className="form-group">
                        <label htmlFor="confluencePage" className="form-label">Project Context Page</label>
                        <select
                            id="confluencePage"
                            className="form-select"
                            value={selectedPageId}
                            onChange={(e) => handlePageChange(e.target.value)}
                        >
                            <option value="">Select a page...</option>
                            {pages.map(page => (
                                <option key={page.id} value={page.id}>
                                    {page.title}
                                </option>
                            ))}
                        </select>
                        <p className="form-help">
                            Select the page that contains project objectives, constraints, stakeholders, and other context information.
                        </p>
                    </div>
                )}
                
                {currentPageConfig && (
                    <div className="form-group">
                        <label className="form-label">Current Configuration</label>
                        <div className="current-config">
                            <p>✅ Project context is currently configured</p>
                            <p className="config-details">Page ID: {currentPageConfig}</p>
                        </div>
                    </div>
                )}
            </div>
            
            {hasUnsavedChanges && (
                <div className="unsaved-warning">
                    ⚠️ You have unsaved changes that will be lost if you switch tabs.
                </div>
            )}
            
            <div className="button-group">
                <button 
                    className="btn-primary"
                    onClick={handleSave}
                    disabled={!selectedPageId}
                >
                    {hasUnsavedChanges ? 'Save Configuration *' : 'Save Configuration'}
                </button>
                <button 
                    className="btn-secondary"
                    onClick={handleClear}
                    disabled={!currentPageConfig}
                >
                    Clear Configuration
                </button>
            </div>
        </div>
    );
}

function TemplateHeader({ currentIssueType, onIssueTypeChange, availableIssueTypes }) {
    return (
        <div className="template-header">
            <div className="issue-type-section">
                <label htmlFor="issueTypeSelect" className="issue-type-label">Issue Type Template:</label>
                <select 
                    id="issueTypeSelect"
                    className="issue-type-select"
                    value={currentIssueType}
                    onChange={(e) => onIssueTypeChange(e.target.value)}
                >
                    {availableIssueTypes.map(issueType => (
                        <option key={issueType.id} value={issueType.name}>
                            {issueType.name}
                        </option>
                    ))}
                </select>
            </div>
        </div>
    );
}

function TemplateEditor({ value, onChange }) {
    return (
        <div className="editor-container">
            <MDEditor
                value={value}
                onChange={(newValue) => onChange(newValue || '')}
                height={400}
                preview="live"
                hideToolbar={false}
                visibleDragBar={false}
                data-color-mode="light"
                placeholder="Loading template..."
            />
        </div>
    );
}

function ButtonGroup({ onSave, onReset, onResetAll, hasUnsavedChanges }) {
    return (
        <div className="button-group">
            <button 
                className="btn-primary"
                onClick={onSave}
            >
                {hasUnsavedChanges ? 'Save Template *' : 'Save Template'}
            </button>
            <button 
                className="btn-secondary"
                onClick={onReset}
            >
                Reset Current to Default
            </button>
            <button 
                className="btn-danger"
                onClick={onResetAll}
            >
                Reset All Templates
            </button>
        </div>
    );
}

function ConfirmationModal({ isOpen, onClose, onConfirm, title, message, confirmText = "Confirm", cancelText = "Cancel", isDangerous = false }) {
    if (!isOpen) return null;
    
    return (
        <div className="modal-overlay" onClick={onClose}>
            <div className="modal-content" onClick={(e) => e.stopPropagation()}>
                <div className="modal-header">
                    <h3 className="modal-title">{title}</h3>
                </div>
                <div className="modal-body">
                    <p className="modal-message">{message}</p>
                </div>
                <div className="modal-footer">
                    <button 
                        className="btn-secondary" 
                        onClick={onClose}
                    >
                        {cancelText}
                    </button>
                    <button 
                        className={isDangerous ? "btn-danger" : "btn-primary"}
                        onClick={onConfirm}
                    >
                        {confirmText}
                    </button>
                </div>
            </div>
        </div>
    );
}

function StatusMessage() {
    const [status, setStatus] = React.useState({ type: '', message: '' });
    
    React.useEffect(() => {
        window.showStatus = (type, message) => {
            setStatus({ type, message });
            
            if (type === 'success' || type === 'info') {
                setTimeout(() => {
                    setStatus({ type: '', message: '' });
                }, 5000);
            }
        };
    }, []);
    
    if (!status.message) return null;
    
    return (
        <div className="status-message">
            <div className={`status-${status.type}`}>
                {status.message}
            </div>
        </div>
    );
}

function showStatus(type, message) {
    if (window.showStatus) {
        window.showStatus(type, message);
    }
}

document.addEventListener('DOMContentLoaded', async function() {
    const root = document.getElementById('root');
    if (!root) {
        console.error('Root element not found');
        return;
    }
    
    try {
        await view.theme.enable();
        const reactRoot = createRoot(root);
        reactRoot.render(<AdminApp />);
    } catch (err) {
        console.warn('Failed to enable theming, continuing without design tokens:', err);
        const reactRoot = createRoot(root);
        reactRoot.render(<AdminApp />);
    }
});