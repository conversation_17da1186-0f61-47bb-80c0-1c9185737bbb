{"name": "jira-issue-action-ui-kit", "version": "1.1.11", "type": "module", "main": "index.js", "license": "MIT", "private": true, "engines": {"node": ">=22.0.0"}, "scripts": {"build": "node scripts/build-prompts.js && npm run build:frontend", "build:frontend": "cd static/admin-page && npm run build", "prebuild": "node scripts/build-prompts.js", "predeploy": "npm run build", "deploy": "forge deploy", "pretest": "npm run build", "pretest:unit": "npm run build", "pretest:integration": "npm run build", "lint": "eslint src/**/*", "lint:fix": "eslint src/**/* --fix", "test": "npm run test:unit && npm run test:validate-config && npm run test:integration", "test:unit": "node --experimental-vm-modules node_modules/.bin/jest test/unit", "test:integration": "node test/integration/run-integration-test.js", "test:validate-config": "node test/integration/validate-config.js", "dev": "forge tunnel", "start": "forge deploy"}, "devDependencies": {"eslint": "^8.56.0", "eslint-plugin-react-hooks": "^4.6.0", "jest": "^29.7.0"}, "dependencies": {"@forge/api": "^3.0.0", "@forge/events": "^1.0.0", "@forge/kvs": "^1.0.2", "@forge/react": "^10.0.0", "@forge/resolver": "1.6.10", "marklassian": "1.0.2"}}